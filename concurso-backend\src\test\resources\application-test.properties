# =====================================================
# TEST CONFIGURATION FOR CV PERSISTENCE TESTS
# =====================================================

# H2 Database Configuration for Fast Unit Tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Enable SQL logging for debugging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.transaction=DEBUG

# CV Module specific logging
logging.level.ar.gov.mpd.concursobackend.experience=DEBUG
logging.level.ar.gov.mpd.concursobackend.education=DEBUG
logging.level.ar.gov.mpd.concursobackend.shared=DEBUG

# Disable unnecessary features for testing
spring.jpa.open-in-view=false
spring.sql.init.mode=never

# Security Configuration for Tests
spring.security.user.name=testuser
spring.security.user.password=testpass
spring.security.user.roles=USER

# Test-specific configurations
test.cv.enable-audit-logging=true
test.cv.enable-soft-delete=true
test.cv.recovery-window-hours=24

# Disable external services for testing
spring.cloud.config.enabled=false
management.endpoints.enabled-by-default=false
