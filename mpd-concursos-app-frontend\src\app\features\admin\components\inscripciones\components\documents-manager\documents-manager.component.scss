@use 'src/styles/variables' as *;

/* Enhanced Glassmorphism Design System Variables - Consistent with Inscriptions Module */
$glass-background-primary: rgba(55, 65, 81, 0.8);
$glass-background-secondary: rgba(75, 85, 99, 0.9);
$glass-background-tertiary: rgba(31, 41, 55, 0.85);
$glass-hover-primary: rgba(75, 85, 99, 0.6);
$glass-active-primary: rgba(55, 65, 81, 0.95);

$text-primary: #f9fafb;
$text-secondary: #d1d5db;
$text-muted: #9ca3af;

$focus-color: #3b82f6;
$focus-color-secondary: rgba(59, 130, 246, 0.2);

// Tema específico para inscripciones
$theme-inscripciones: #4CAF50;
$theme-inscripciones-light: rgba(76, 175, 80, 0.1);
$theme-inscripciones-dark: #388E3C;

$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

$border-glass-light: 1px solid rgba(255, 255, 255, 0.1);
$border-glass-medium: 1px solid rgba(255, 255, 255, 0.15);
$border-glass-strong: 1px solid rgba(255, 255, 255, 0.2);

$card-shadow-base: 0 8px 32px rgba(0, 0, 0, 0.3);
$card-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);
$card-shadow-focus: 0 0 0 3px rgba(76, 175, 80, 0.3);

$transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

.documents-manager-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  /* Transparent background to inherit from admin layout */
  background: transparent;
  min-height: 100vh;
  color: $text-primary;
}

/* Enhanced Header with Glassmorphism */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;

  /* Premium glassmorphism background */
  background: linear-gradient(135deg,
    $glass-background-primary 0%,
    $glass-background-secondary 100%);
  background-image:
    linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
    radial-gradient(circle at 80% 20%, $theme-inscripciones-light 0%, transparent 50%);
  border: $border-glass-medium;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: $border-radius-lg;
  box-shadow:
    $card-shadow-base,
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: $transition-smooth;

  h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 0.75rem;

    i {
      color: $theme-inscripciones;
      font-size: 1.5rem;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }
}

/* Enhanced Content Card with Glassmorphism */
.content-card {
  /* Premium glassmorphism base */
  background: linear-gradient(135deg,
    $glass-background-primary 0%,
    $glass-background-secondary 100%);
  background-image:
    linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
    radial-gradient(circle at 90% 10%, $theme-inscripciones-light 0%, transparent 50%);
  border: $border-glass-medium;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: $border-radius-lg;
  box-shadow:
    $card-shadow-base,
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: $transition-smooth;

  .tab-content {
    padding: 1.5rem;
    background: transparent;
  }
}

/* Enhanced Tabs with Glassmorphism */
.tabs {
  display: flex;
  background: linear-gradient(135deg,
    rgba(31, 41, 55, 0.9) 0%,
    rgba(55, 65, 81, 0.8) 100%);
  border-bottom: $border-glass-light;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  .tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: $text-secondary;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: $transition-fast;
    border-bottom: 3px solid transparent;
    position: relative;

    i {
      font-size: 1rem;
      color: $text-muted;
      transition: $transition-fast;
    }

    &:hover:not(:disabled) {
      background: rgba(76, 175, 80, 0.1);
      color: $text-primary;

      i {
        color: $theme-inscripciones;
      }
    }

    &.active {
      background: linear-gradient(135deg,
        rgba(76, 175, 80, 0.2) 0%,
        rgba(76, 175, 80, 0.1) 100%);
      color: $text-primary;
      border-bottom-color: $theme-inscripciones;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);

      i {
        color: $theme-inscripciones;
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      color: $text-muted;

      &:hover {
        background: transparent;
        color: $text-muted;

        i {
          color: $text-muted;
        }
      }
    }
  }
}

/* Old filter card styles removed - replaced with glassmorphism version */

/* Old Material UI filter form styles removed - replaced with glassmorphism version */

/* Old bulk actions styles removed - replaced with glassmorphism version */

/* Old table container styles removed - replaced with glassmorphism version */

/* Old Material UI table styles removed - replaced with glassmorphism version */

/* Old Material UI column styles removed - replaced with glassmorphism version */

  .action-buttons {
    display: flex !important;
    gap: 4px !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 4px !important;
    min-height: 40px !important;

    app-custom-button {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;

      .custom-button {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        padding: 6px !important;
        min-width: 28px !important;
        height: 28px !important;
        width: 28px !important;
        border-radius: 6px !important;
        font-size: 12px !important;
        transition: all 0.2s ease !important;
        border: 1px solid rgba(255,255,255,0.1) !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        background: rgba(255,255,255,0.05) !important;
        color: #b0b0b0 !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

        &.primary.icon {
          background: rgba(#1976D2, 0.15) !important;
          color: #1976D2 !important;
          border-color: rgba(#1976D2, 0.4) !important;
        }
        &.success.icon {
          background: rgba(#4CAF50, 0.15) !important;
          color: #4CAF50 !important;
          border-color: rgba(#4CAF50, 0.4) !important;
        }
        &.danger.icon {
          background: rgba(#f44336, 0.15) !important;
          color: #f44336 !important;
          border-color: rgba(#f44336, 0.4) !important;
        }
        &.warn.icon {
          background: rgba(#FF9800, 0.15) !important;
          color: #FF9800 !important;
          border-color: rgba(#FF9800, 0.4) !important;
        }

        &:hover:not(:disabled) {
          transform: translateY(-1px) !important;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
        }
        &.primary.icon:hover:not(:disabled) {
          background: #1976D2 !important;
          color: white !important;
          border-color: #1976D2 !important;
          box-shadow: 0 3px 8px rgba(#1976D2, 0.4) !important;
        }
        &.success.icon:hover:not(:disabled) {
          background: #4CAF50 !important;
          color: white !important;
          border-color: #4CAF50 !important;
          box-shadow: 0 3px 8px rgba(#4CAF50, 0.4) !important;
        }
        &.danger.icon:hover:not(:disabled) {
          background: #f44336 !important;
          color: white !important;
          border-color: #f44336 !important;
          box-shadow: 0 3px 8px rgba(#f44336, 0.4) !important;
        }
        &.warn.icon:hover:not(:disabled) {
          background: #FF9800 !important;
          color: white !important;
          border-color: #FF9800 !important;
          box-shadow: 0 3px 8px rgba(#FF9800, 0.4) !important;
        }
        &:disabled {
          opacity: 0.4 !important;
          cursor: not-allowed !important;
          background: rgba(66,66,66,0.3) !important;
          color: #888 !important;
          &:hover {
            transform: none !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
          }
        }
        i {
          font-size: 12px !important;
          line-height: 1 !important;
          margin: 0 !important;
          display: block !important;
          visibility: visible !important;
        }
      }
    }
  }

/* Old cell styles removed - replaced with glassmorphism version */

/* Old loading and empty state styles removed - replaced with glassmorphism version */

/* Old document viewer tab styles removed - replaced with glassmorphism version */

/* Old Material UI tab styles removed - replaced with glassmorphism version */

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .documents-manager-container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;

    h1 {
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }

  .filter-row {
    grid-template-columns: 1fr !important;
    gap: 1rem;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;

    .selection-info {
      text-align: center;
    }

    .action-buttons {
      width: 100%;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .table-container {
    overflow-x: auto;

    .custom-table {
      min-width: 700px;
    }
  }

  .tabs {
    .tab {
      font-size: 0.75rem;
      padding: 0.75rem 1rem;

      i {
        font-size: 0.75rem;
      }
    }
  }

  /* Enhanced document viewer tab layout */
  .document-viewer-tab {
    .action-buttons {
      justify-content: flex-start !important;
      gap: 0.75rem !important;

      app-custom-button {
        flex: none !important;
        min-width: auto !important;
        max-width: none !important;
      }
    }
  }
}

/* Old dark theme styles removed - glassmorphism design is inherently dark */

/* Enhanced Document Viewer Tab Styles */
.document-viewer-tab {
  /* Override any inherited styles that might center the buttons */
  app-document-viewer {
    .document-details {
      .actions-section {
        .action-buttons {
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          gap: 1rem !important;
          width: 100% !important;
          flex-wrap: wrap !important;

          app-custom-button {
            flex: 1 !important;
            max-width: 180px !important;
            min-width: 120px !important;
          }

          /* Responsive behavior for smaller screens */
          @media (max-width: 768px) {
            flex-direction: column !important;
            gap: 0.75rem !important;

            app-custom-button {
              width: 100% !important;
              max-width: none !important;
            }
          }
        }
      }
    }
  }
}

/* Enhanced Accessibility with Glassmorphism */
.documents-manager-container {
  // WCAG AA compliance
  * {
    &:focus {
      outline: 2px solid $theme-inscripciones;
      outline-offset: 2px;
      border-radius: $border-radius-sm;
    }
  }

  // Enhanced readability
  input, select, button {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  // Smooth animations
  * {
    transition: $transition-fast;
  }

  // High contrast for better visibility
  .input, .button {
    &:focus {
      box-shadow: $card-shadow-focus;
    }
  }
}

/* Old custom table styles removed - replaced with enhanced glassmorphism version */

/* Old button styles removed - replaced with enhanced glassmorphism version */

/* Duplicate tabs and input styles removed - already implemented in glassmorphism version above */

/* Enhanced Filter Card with Glassmorphism */
.filter-card {
  margin-bottom: 1.5rem;
  padding: 1.5rem;

  /* Premium glassmorphism background */
  background: linear-gradient(135deg,
    $glass-background-tertiary 0%,
    $glass-background-primary 100%);
  background-image:
    linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%),
    radial-gradient(circle at 70% 30%, $theme-inscripciones-light 0%, transparent 50%);
  border: $border-glass-light;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: $border-radius-lg;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: $transition-smooth;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

/* Enhanced Input Groups */
.input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: $text-muted;
    font-size: 0.875rem;
    z-index: 2;
  }

  .input {
    background: rgba(31, 41, 55, 0.6);
    border: $border-glass-light;
    border-radius: $border-radius-md;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    color: $text-primary;
    font-size: 0.875rem;
    transition: $transition-fast;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);

    &::placeholder {
      color: $text-muted;
    }

    &:focus {
      outline: none;
      border-color: $theme-inscripciones;
      background: rgba(31, 41, 55, 0.8);
      box-shadow: $card-shadow-focus;
    }

    &:hover {
      border-color: rgba(255, 255, 255, 0.2);
      background: rgba(31, 41, 55, 0.7);
    }
  }
}

/* Enhanced Bulk Actions */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;

  /* Premium glassmorphism background with green theme */
  background: linear-gradient(135deg,
    rgba(76, 175, 80, 0.15) 0%,
    rgba(76, 175, 80, 0.08) 100%);
  border: 1px solid rgba(76, 175, 80, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: $border-radius-lg;
  box-shadow:
    0 4px 16px rgba(76, 175, 80, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  .selection-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: $text-primary;

    i {
      color: $theme-inscripciones;
      font-size: 1rem;
    }
  }

  .action-buttons {
    display: flex;
    gap: 0.75rem;
  }
}

/* Enhanced Empty State with Glassmorphism */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;

  /* Premium glassmorphism background */
  background: linear-gradient(135deg,
    rgba(31, 41, 55, 0.6) 0%,
    rgba(55, 65, 81, 0.4) 100%);
  border: 2px dashed rgba(76, 175, 80, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: $border-radius-lg;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: $text-muted;
    opacity: 0.7;
  }

  p {
    margin-bottom: 2rem;
    color: $text-secondary;
    text-align: center;
    font-size: 1rem;
    line-height: 1.5;
  }
}
/* Duplicate loading and paginator styles removed - already implemented in glassmorphism version above */

/* Enhanced Table Container with Glassmorphism */
.table-container {
  position: relative;
  min-height: 400px;
  border-radius: $border-radius-lg;
  overflow: hidden;

  /* Premium glassmorphism background */
  background: linear-gradient(135deg,
    $glass-background-primary 0%,
    $glass-background-secondary 100%);
  border: $border-glass-medium;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow:
    $card-shadow-base,
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  @media (max-width: 900px) {
    overflow-x: auto;
    .custom-table { min-width: 700px; }
  }
}

/* Enhanced Custom Table with Glassmorphism */
.custom-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  color: $text-primary;

  thead {
    background: linear-gradient(135deg,
      rgba(31, 41, 55, 0.9) 0%,
      rgba(55, 65, 81, 0.8) 100%);

    th {
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      color: $text-primary;
      border-bottom: $border-glass-light;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &:first-child {
        width: 50px;
        text-align: center;
      }

      &:last-child {
        width: 120px;
        text-align: center;
      }
    }
  }

  tbody {
    tr {
      background: transparent;
      border-bottom: $border-glass-light;
      transition: $transition-fast;
      cursor: pointer;

      &:hover {
        background: rgba(75, 85, 99, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.clickable-row:hover {
        background: rgba(76, 175, 80, 0.1);
      }
    }

    td {
      padding: 1rem;
      color: $text-primary;
      font-size: 0.875rem;
      vertical-align: middle;
      border-bottom: $border-glass-light;

      &:first-child {
        text-align: center;

        input[type="checkbox"] {
          width: 16px;
          height: 16px;
          accent-color: $theme-inscripciones;
        }
      }

      &:last-child {
        text-align: center;
      }
    }
  }
}

/* Enhanced File Name Cell */
.file-name-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .file-icon {
    color: $theme-inscripciones;
    font-size: 1.25rem;
  }

  .file-name {
    font-weight: 500;
    color: $text-primary;
  }
}

/* Enhanced Date Cell */
.date-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .upload-date {
    color: $text-primary;
    font-weight: 500;
  }

  .review-date {
    color: $text-muted;
    font-size: 0.75rem;
  }
}

/* Enhanced Document Type */
.document-type {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border-radius: $border-radius-sm;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Enhanced Button Styles */
.button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: $border-radius-md;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: $transition-fast;
  text-decoration: none;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  i {
    font-size: 0.875rem;
  }

  &.button-primary {
    background: linear-gradient(135deg, $theme-inscripciones 0%, $theme-inscripciones-dark 100%);
    color: white;
    border: 1px solid $theme-inscripciones;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);

    &:hover {
      background: linear-gradient(135deg, $theme-inscripciones-dark 0%, darken($theme-inscripciones-dark, 10%) 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
    }
  }

  &.button-secondary {
    background: rgba(75, 85, 99, 0.8);
    color: $text-primary;
    border: $border-glass-light;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

    &:hover {
      background: rgba(75, 85, 99, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }
  }

  &.button-success {
    background: linear-gradient(135deg, $theme-inscripciones 0%, $theme-inscripciones-dark 100%);
    color: white;
    border: 1px solid $theme-inscripciones;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);

    &:hover {
      background: linear-gradient(135deg, $theme-inscripciones-dark 0%, darken($theme-inscripciones-dark, 10%) 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
    }
  }

  &.button-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: 1px solid #ef4444;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

    &:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
    }
  }
}

/* Enhanced Document Viewer Tab */
.document-viewer-tab {
  padding: 0 !important;
  background: transparent;

  .no-document-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;

    /* Premium glassmorphism background */
    background: linear-gradient(135deg,
      rgba(31, 41, 55, 0.6) 0%,
      rgba(55, 65, 81, 0.4) 100%);
    border: 2px dashed rgba(76, 175, 80, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: $border-radius-lg;
    margin: 1rem;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
      color: $text-muted;
      opacity: 0.7;
    }

    p {
      margin-bottom: 2rem;
      color: $text-secondary;
      text-align: center;
      font-size: 1.1rem;
      line-height: 1.5;
    }
  }
}

/* Enhanced Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: $border-radius-lg;

  .loader {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(76, 175, 80, 0.3);
    border-top: 4px solid $theme-inscripciones;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Paginator Container */
.paginator-container {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  background: linear-gradient(135deg,
    rgba(31, 41, 55, 0.6) 0%,
    rgba(55, 65, 81, 0.4) 100%);
  border-top: $border-glass-light;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

// ESTILOS FORZADOS PARA BOTONES DE ACCIÓN - IGUAL QUE EN CONCURSOS
::ng-deep .action-buttons {
  display: flex !important;
  gap: 4px !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 4px !important;
  min-height: 40px !important;

  app-custom-button {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;

    .custom-button {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      padding: 6px !important;
      min-width: 28px !important;
      height: 28px !important;
      width: 28px !important;
      border-radius: 6px !important;
      font-size: 12px !important;
      transition: all 0.2s ease !important;
      border: 1px solid rgba(255,255,255,0.1) !important;
      align-items: center !important;
      justify-content: center !important;
      cursor: pointer !important;
      background: rgba(255,255,255,0.05) !important;
      color: #b0b0b0 !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

      &.primary.icon {
        background: rgba(#1976D2, 0.15) !important;
        color: #1976D2 !important;
        border-color: rgba(#1976D2, 0.4) !important;
      }
      &.success.icon {
        background: rgba(#4CAF50, 0.15) !important;
        color: #4CAF50 !important;
        border-color: rgba(#4CAF50, 0.4) !important;
      }
      &.danger.icon {
        background: rgba(#f44336, 0.15) !important;
        color: #f44336 !important;
        border-color: rgba(#f44336, 0.4) !important;
      }
      &.warn.icon {
        background: rgba(#FF9800, 0.15) !important;
        color: #FF9800 !important;
        border-color: rgba(#FF9800, 0.4) !important;
      }

      &:hover:not(:disabled) {
        transform: translateY(-1px) !important;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
      }
      &.primary.icon:hover:not(:disabled) {
        background: #1976D2 !important;
        color: white !important;
        border-color: #1976D2 !important;
        box-shadow: 0 3px 8px rgba(#1976D2, 0.4) !important;
      }
      &.success.icon:hover:not(:disabled) {
        background: #4CAF50 !important;
        color: white !important;
        border-color: #4CAF50 !important;
        box-shadow: 0 3px 8px rgba(#4CAF50, 0.4) !important;
      }
      &.danger.icon:hover:not(:disabled) {
        background: #f44336 !important;
        color: white !important;
        border-color: #f44336 !important;
        box-shadow: 0 3px 8px rgba(#f44336, 0.4) !important;
      }
      &.warn.icon:hover:not(:disabled) {
        background: #FF9800 !important;
        color: white !important;
        border-color: #FF9800 !important;
        box-shadow: 0 3px 8px rgba(#FF9800, 0.4) !important;
      }
      &:disabled {
        opacity: 0.4 !important;
        cursor: not-allowed !important;
        background: rgba(66,66,66,0.3) !important;
        color: #888 !important;
        &:hover {
          transform: none !important;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }
      }
      i {
        font-size: 12px !important;
        line-height: 1 !important;
        margin: 0 !important;
        display: block !important;
        visibility: visible !important;
      }
    }
  }
}

/* Estilos para información del usuario */
.user-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .user-name {
    font-weight: 500;
    color: $text-primary;
    font-size: 0.9rem;
  }

  .user-dni {
    font-size: 0.8rem;
    color: $text-muted;
  }

  .user-detail-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.1));
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: $border-radius-sm;
    font-size: 0.75rem;
    cursor: pointer;
    transition: $transition-fast;
    backdrop-filter: blur(8px);

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(37, 99, 235, 0.15));
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    i {
      font-size: 0.8rem;
    }
  }
}

/* Estilos para categorías de documentos */
.document-category {
  padding: 4px 8px;
  border-radius: $border-radius-sm;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  transition: $transition-fast;

  &.category-obligatory {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
    backdrop-filter: blur(8px);
  }

  &.category-cv_proof {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.1));
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(8px);
  }

  &.category-optional {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.15), rgba(147, 51, 234, 0.1));
    color: #c4b5fd;
    border: 1px solid rgba(168, 85, 247, 0.3);
    backdrop-filter: blur(8px);
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}
