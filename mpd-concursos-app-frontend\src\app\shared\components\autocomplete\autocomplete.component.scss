/* Autocomplete Component - Estilo glassmorphism coherente con el formulario */

.autocomplete-container {
  position: relative;
  width: 100%;
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.08);
  }
  
  &.focused {
    border-color: #00d4ff;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
  }
  
  &.has-error {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
  }
}

.autocomplete-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  font-family: inherit;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
    transition: opacity 0.3s ease;
  }
  
  &:focus::placeholder {
    opacity: 0.7;
  }
  
  &:disabled {
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
  }
}

.clear-button,
.dropdown-button {
  background: transparent;
  border: none;
  padding: 8px;
  margin-right: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.clear-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  
  &:hover {
    color: #ff6b6b;
  }
}

.dropdown-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  transition: transform 0.3s ease;
  
  &.rotated {
    transform: rotate(180deg);
  }
}

.loading-indicator {
  padding: 8px;
  margin-right: 4px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dropdown */
.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  max-height: 300px;
  overflow-y: auto;
  animation: dropdownSlideIn 0.2s ease-out;
  
  /* Scrollbar personalizado */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover,
  &.highlighted {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.2);
  }
  
  &:active {
    background: rgba(0, 212, 255, 0.2);
  }
}

.item-text {
  flex: 1;
  color: #ffffff;
  font-size: 14px;
  
  /* Estilos para el highlight de búsqueda */
  :global(mark) {
    background: rgba(0, 212, 255, 0.3);
    color: #ffffff;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
  }
}

.item-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-left: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Mensajes informativos */
.no-results,
.min-query-message,
.help-message {
  padding: 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.no-results-icon,
.min-query-icon,
.help-icon {
  font-size: 24px;
  opacity: 0.8;
}

.no-results-text,
.min-query-text,
.help-text {
  font-size: 13px;
  line-height: 1.4;
}

/* Mensaje de error */
.error-message {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 6px;
  color: #ff6b6b;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: errorSlideIn 0.3s ease-out;
}

.error-icon {
  font-size: 14px;
}

.error-text {
  flex: 1;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Texto de ayuda */
.help-text {
  margin-top: 6px;
  padding: 6px 12px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
  
  .help-icon {
    font-size: 12px;
    opacity: 0.7;
  }
}

/* Estados de accesibilidad */
.autocomplete-input:focus-visible {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

.dropdown-item:focus-visible {
  outline: 2px solid #00d4ff;
  outline-offset: -2px;
}

/* Responsive */
@media (max-width: 768px) {
  .dropdown {
    max-height: 250px;
  }
  
  .dropdown-item {
    padding: 14px 16px;
  }
  
  .item-text {
    font-size: 15px;
  }
  
  .item-info {
    font-size: 11px;
  }
}

/* Modo oscuro adicional */
@media (prefers-color-scheme: dark) {
  .dropdown {
    background: rgba(20, 20, 20, 0.95);
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .input-wrapper {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* Animaciones reducidas para accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .dropdown,
  .error-message,
  .spinner,
  .dropdown-icon {
    animation: none;
    transition: none;
  }
  
  .dropdown-icon.rotated {
    transform: none;
  }
}
