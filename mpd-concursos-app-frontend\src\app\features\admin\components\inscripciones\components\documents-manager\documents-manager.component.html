<div class="documents-manager-container">
  <div class="header">
    <h1>
      <i class="fas fa-file-alt"></i>
      Gestión de Documentos
    </h1>
    <div class="header-actions">
      <a class="button button-primary" routerLink="/admin/inscripciones/dashboard">
        <i class="fas fa-home"></i>
        Dashboard
      </a>
    </div>
  </div>

  <div class="content-card">
    <div class="tabs">
      <button class="tab" [class.active]="activeTab === 0" (click)="setActiveTab(0)">
        <i class="fas fa-list"></i>
        Listado de Documentos
      </button>
      <button class="tab" [class.active]="activeTab === 1" (click)="setActiveTab(1)" [disabled]="!selectedDocument">
        <i class="fas fa-eye"></i>
        Visor de Documento
      </button>
    </div>
    <div class="tab-content" *ngIf="activeTab === 0">
      <div class="filter-card">
        <form [formGroup]="filterForm" class="filter-form">
          <div class="filter-row">
            <div class="input-group">
              <i class="fas fa-search input-icon"></i>
              <input class="input" type="text" formControlName="search" placeholder="Buscar por nombre, tipo...">
            </div>
            <div class="input-group">
              <i class="fas fa-user input-icon"></i>
              <input class="input" type="text" formControlName="userSearch" placeholder="Buscar por usuario (DNI, nombre, email)">
            </div>
            <div class="input-group">
              <i class="fas fa-filter input-icon"></i>
              <select class="input" formControlName="status">
                <option *ngFor="let option of statusOptions" [value]="option.value">{{option.label}}</option>
              </select>
            </div>
            <div class="input-group">
              <i class="fas fa-tags input-icon"></i>
              <select class="input" formControlName="documentCategory">
                <option value="">Todas las categorías</option>
                <option value="OBLIGATORY">Documentación Obligatoria</option>
                <option value="CV_PROOF">Probanza de CV</option>
                <option value="OPTIONAL">Documentación Opcional</option>
              </select>
            </div>
            <div class="input-group">
              <i class="fas fa-file-alt input-icon"></i>
              <select class="input" formControlName="documentType">
                <option value="">Todos los tipos</option>
                <option *ngFor="let type of documentTypes" [value]="type.id">{{type.name}}</option>
              </select>
            </div>
            <button type="button" class="button button-secondary" (click)="resetFilters()">
              <i class="fas fa-times"></i>
              Limpiar Filtros
            </button>
          </div>
        </form>
      </div>
      <div class="bulk-actions" *ngIf="getSelectedCount() > 0">
        <div class="selection-info">
          <i class="fas fa-check-circle"></i>
          <span>{{getSelectedCount()}} documento(s) seleccionado(s)</span>
        </div>
        <div class="action-buttons">
          <button class="button button-success" (click)="approveSelectedDocuments()">
            <i class="fas fa-check"></i>
            Aprobar Seleccionados
          </button>
          <button class="button button-danger" (click)="rejectSelectedDocuments()">
            <i class="fas fa-times"></i>
            Rechazar Seleccionados
          </button>
        </div>
      </div>
      <div class="table-container">
        <table class="custom-table">
          <thead>
            <tr>
              <th><input type="checkbox" [checked]="allSelected" [indeterminate]="getSelectedCount() > 0 && !allSelected" (change)="toggleAllSelection()"></th>
              <th>Nombre</th>
              <th>Tipo</th>
              <th>Usuario</th>
              <th>Categoría</th>
              <th>Fecha de Carga</th>
              <th>Estado</th>
              <th>Acciones</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let document of dataSource" [class.clickable-row]="true" (click)="viewDocument(document)">
              <td><input type="checkbox" [checked]="selectedDocuments[document.id]" (click)="$event.stopPropagation()" (change)="toggleSelection(document)"></td>
              <td>
                <div class="file-name-cell">
                  <i class="fas fa-file-pdf file-icon"></i>
                  <span class="file-name">{{document.fileName}}</span>
                </div>
              </td>
              <td><span class="document-type">{{document.documentType}}</span></td>
              <td>
                <div class="user-info-cell">
                  <span class="user-name">{{getUserName(document)}}</span>
                  <span class="user-dni">DNI: {{getUserDni(document)}}</span>
                  <button class="user-detail-button"
                          (click)="openUserDocumentsDetail(document); $event.stopPropagation()"
                          title="Ver toda la documentación del usuario">
                    <i class="fas fa-user-circle"></i>
                    Ver Documentación
                  </button>
                </div>
              </td>
              <td>
                <span class="document-category" [class]="getDocumentCategoryClass(document)">
                  {{getDocumentCategoryLabel(document)}}
                </span>
              </td>
              <td>
                <div class="date-cell">
                  <span class="upload-date">{{formatDate(document.uploadDate)}}</span>
                  <span class="review-date" *ngIf="document.reviewDate">Revisado: {{formatDate(document.reviewDate)}}</span>
                </div>
              </td>
              <td>
                <app-contest-status-badge
                  [status]="document.status"
                  [statusType]="'document'"
                  [showIcon]="true">
                </app-contest-status-badge>
              </td>
              <td>
                <div class="action-buttons">
                  <app-custom-button
                    [icon]="'eye'"
                    [variant]="'icon'"
                    [color]="'primary'"
                    [tooltip]="'Ver Documento'"
                    (buttonClick)="viewDocument(document); $event.stopPropagation()">
                  </app-custom-button>
                  <app-custom-button
                    *ngIf="document.status === 'PENDING'"
                    [icon]="'check'"
                    [variant]="'icon'"
                    [color]="'success'"
                    [tooltip]="'Aprobar'"
                    (buttonClick)="approveDocument(document); $event.stopPropagation()">
                  </app-custom-button>
                  <app-custom-button
                    *ngIf="document.status === 'PENDING'"
                    [icon]="'times'"
                    [variant]="'icon'"
                    [color]="'danger'"
                    [tooltip]="'Rechazar'"
                    (buttonClick)="rejectDocument(document); $event.stopPropagation()">
                  </app-custom-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div *ngIf="isLoading" class="loading-overlay">
          <span class="loader"></span>
        </div>
        <div *ngIf="!isLoading && dataSource.length === 0" class="empty-state">
          <i class="fas fa-file-alt empty-icon"></i>
          <p>No se encontraron documentos con los filtros aplicados</p>
          <button class="button button-primary" (click)="resetFilters()">
            <i class="fas fa-refresh"></i>
            Limpiar Filtros
          </button>
        </div>
        <div class="paginator-container">
          <app-custom-paginator [length]="totalItems" [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions" [pageIndex]="pageIndex" (page)="onPageChange($event)"></app-custom-paginator>
        </div>
      </div>
    </div>
    <div class="tab-content document-viewer-tab" *ngIf="activeTab === 1">
      <div *ngIf="!selectedDocument" class="no-document-selected">
        <i class="fas fa-file-alt empty-icon"></i>
        <p>Seleccione un documento para visualizarlo</p>
        <button class="button button-primary" (click)="setActiveTab(0)">
          <i class="fas fa-arrow-left"></i>
          Volver al Listado
        </button>
      </div>
      <app-document-viewer *ngIf="selectedDocument" [document]="selectedDocument"></app-document-viewer>
    </div>
  </div>
</div>
