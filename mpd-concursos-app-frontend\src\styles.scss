@use '@angular/material' as mat;
@use 'sass:map';

/* === FUENTES LOCALES === */
/* Importar fuentes desde node_modules para evitar dependencias externas */
@import '@fontsource/roboto/300.css';
@import '@fontsource/roboto/400.css';
@import '@fontsource/roboto/500.css';
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* === ANIMACIONES DE ICONOS === */
@import 'styles/icon-animations.scss';

/* === UNIFIED GLASSMORPHISM SYSTEM === */
/* Sistema glassmorphism unificado que reemplaza todos los sistemas anteriores */
@import './styles/unified-glassmorphism-system';

/* === DIRECTIVAS DE VALIDACIÓN === */
/* Estilos para directivas de restricción de entrada */
@import './app/shared/directives/input-restriction.directive.scss';

/* === SKIP LINKS PARA ACCESIBILIDAD === */
.skip-links {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 9999;

  .skip-link {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--color-focus);
    color: var(--color-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    text-decoration: none;
    font-weight: 600;
    border-radius: 0 0 var(--radius-md) 0;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-fast);

    &:focus {
      top: 0;
      outline: 2px solid var(--color-primary);
      outline-offset: 2px;
    }

    &:hover {
      background: var(--color-info);
      transform: var(--transform-hover);
    }
  }
}

/* === LEGACY IMPORTS === */
/* Mantener solo las importaciones necesarias que no están en el sistema unificado */
@import './styles/responsive';
@import './styles/touch';
@import './styles/dialog-styles';
@import './styles/global';
@import './styles/fix-styles';

/* === PDF VIEWER GLOBAL STYLES === */
/* Estilos globales para ng2-pdf-viewer con posicionamiento absoluto requerido */
.ng2-pdf-viewer-container {
  position: absolute !important; /* ✅ CRÍTICO: ng2-pdf-viewer requiere position: absolute */
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  overflow-x: hidden !important;
  overflow-y: auto !important; /* ✅ Scroll vertical habilitado */
  background: #f5f5f5 !important;
  border-radius: 8px !important;
  z-index: 1 !important;
}

pdf-viewer {
  position: absolute !important; /* ✅ CRÍTICO: pdf-viewer también necesita posicionamiento absoluto */
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

.pdfViewer {
  padding: 20px !important;
  width: 100% !important;
  height: auto !important;
  min-height: 100% !important;
  overflow: visible !important;
}

.pdfViewer .page {
  margin: 0 auto 20px auto !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}
/* Importaciones corregidas para los estilos de usuarios */
@import './app/features/admin/components/usuarios/styles/user-dialog-button-fixes';
@import './app/features/admin/components/usuarios/styles/user-select-fixes';
@import './app/shared/components/custom-form/custom-dialog/dialog-button-fixes';
@import './app/shared/components/custom-form/custom-select/select-fixes';
@import './app/shared/components/custom-form/custom-checkbox/checkbox-fixes';
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// Definición de paletas compatible con Angular Material
$custom-primary: mat.define-palette(mat.$indigo-palette);
$custom-accent: mat.define-palette(mat.$blue-palette);
$custom-warn: mat.define-palette(mat.$red-palette);

// Definir el tema una sola vez
$dark-theme: mat.define-dark-theme((
  color: (
    primary: $custom-primary,
    accent: $custom-accent,
    warn: $custom-warn,
  ),
  typography: mat.define-typography-config(),
  density: 0,
));

// Incluir core y temas una sola vez a nivel global
@include mat.core();
@include mat.all-component-themes($dark-theme);

// Variables globales
:root {
  --surface-color: 30, 30, 30;
  --surface-color-rgb: var(--surface-color);
  --primary-color: #{mat.get-color-from-palette($custom-primary)};
  --text-color: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --card-background: var(--surface-color);
  --card-border: rgba(255, 255, 255, 0.1);
  --card-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
  --card-backdrop-filter: blur(8px);
  --border-radius: 8px;
  --background-color: rgba(55, 65, 81, 0.9);
  --background-color-rgb: 18, 18, 18;

  /* Variables para la tabla personalizada */
  --custom-surface: #424242;
  --custom-surface-dark: #333333;
  --custom-background: #333333;
  --custom-background-dark: #222222;
  --custom-background-light: #424242;
  --custom-primary: #1976D2;
  --custom-primary-dark: #1565C0;
  --custom-primary-light: rgba(25, 118, 210, 0.15);
  --custom-text-primary: #FFFFFF;
  --custom-text-primary-dark: #E0E0E0;
  --custom-text-secondary: #B0B0B0;
  --custom-text-secondary-dark: #A0A0A0;
  --custom-border: #484848;
  --custom-border-dark: #333333;
  --custom-hover: #3A3A3A;
  --custom-success: #4caf50;
  --custom-success-dark: #388e3c;
  --custom-error: #f44336;
  --custom-error-dark: #d32f2f;
  --custom-warning: #ff9800;
  --custom-warning-dark: #f57c00;
  --custom-info: #2196F3;
  --custom-info-dark: #1976D2;
  --custom-accent: #4CAF50;
  --custom-accent-dark: #388E3C;
  --custom-text-disabled: #666666;
  --custom-border-radius: 8px;
  --custom-border-radius-sm: 4px;

  /* Variables para el componente app-custom-table */
  --color-surface: #424242;
  --color-surface-dark: #333333;
  --color-surface-rgb: 66, 66, 66;
  --color-surface-dark-rgb: 51, 51, 51;
  --color-background: #333333;
  --color-background-dark: #222222;
  --color-background-rgb: 51, 51, 51;
  --color-background-dark-rgb: 34, 34, 34;
  --color-background-light: #424242;
  --color-background-light-rgb: 66, 66, 66;
  --color-background-hover: #3A3A3A;
  --color-background-hover-dark: #444444;
  --color-background-hover-rgb: 58, 58, 58;
  --color-background-hover-dark-rgb: 68, 68, 68;
  --color-primary: #1976D2;
  --color-primary-dark: #1565C0;
  --color-primary-light: rgba(25, 118, 210, 0.15);
  --color-primary-rgb: 25, 118, 210;
  --color-primary-dark-rgb: 21, 101, 192;
  --color-text-primary: #FFFFFF;
  --color-text-primary-dark: #E0E0E0;
  --color-text-primary-rgb: 255, 255, 255;
  --color-text-primary-dark-rgb: 224, 224, 224;
  --color-text-secondary: #B0B0B0;
  --color-text-secondary-dark: #A0A0A0;
  --color-text-secondary-rgb: 176, 176, 176;
  --color-text-secondary-dark-rgb: 160, 160, 160;
  --color-text-disabled: #666666;
  --color-text-disabled-dark: #666666;
  --color-text-disabled-rgb: 102, 102, 102;
  --color-border: #484848;
  --color-border-dark: #333333;
  --color-border-rgb: 72, 72, 72;
  --color-border-dark-rgb: 51, 51, 51;
  --color-success: #4caf50;
  --color-success-dark: #388e3c;
  --color-success-rgb: 76, 175, 80;
  --color-success-dark-rgb: 56, 142, 60;
  --color-error: #f44336;
  --color-error-dark: #d32f2f;
  --color-error-rgb: 244, 67, 54;
  --color-error-dark-rgb: 211, 47, 47;
  --color-warning: #ff9800;
  --color-warning-dark: #f57c00;
  --color-warning-rgb: 255, 152, 0;
  --color-warning-dark-rgb: 245, 124, 0;

  /* ===== LEGACY VARIABLES ===== */
  /* Variables mantenidas para compatibilidad con componentes existentes */
  /* Estas serán migradas gradualmente al sistema unificado */
}

// Estilos globales
html, body {
  height: 100%;
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
}

body {
  overflow: hidden;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

main {
  flex: 1;
  background-color: var(--background-color);
  min-height: 0;
  position: relative;
}

// Note: Color palettes are generated from primary: #8e6f6e, secondary: #f0f8ff, tertiary: #cce4ff, neutral: #f5f5f5
$_palettes: (
  primary: (
    0: #000000,
    10: #2b1616,
    20: #422a2a,
    25: #4e3534,
    30: #5a403f,
    35: #674c4b,
    40: #745756,
    50: #8f706f,
    60: #aa8988,
    70: #c6a3a2,
    80: #e3bebc,
    90: #ffdad8,
    95: #ffedeb,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #151d22,
    20: #293137,
    25: #343c42,
    30: #40484d,
    35: #4b5359,
    40: #575f65,
    50: #70787e,
    60: #8a9298,
    70: #a4acb3,
    80: #bfc8ce,
    90: #dbe4ea,
    95: #eaf2f9,
    98: #f5faff,
    99: #fbfcff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #031d31,
    20: #1b3247,
    25: #263d53,
    30: #32495f,
    35: #3e556b,
    40: #4a6177,
    50: #627991,
    60: #7c93ac,
    70: #96aec7,
    80: #b1c9e3,
    90: #cee5ff,
    95: #e8f2ff,
    98: #f7f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    4: #0a0b0b,
    6: #101111,
    10: #1a1c1c,
    12: #1e2020,
    17: #292b2b,
    20: #2f3131,
    22: #333535,
    24: #383a3a,
    25: #3a3c3c,
    30: #454747,
    35: #515353,
    40: #5d5f5f,
    50: #767777,
    60: #909191,
    70: #aaabab,
    80: #c6c6c7,
    87: #dadada,
    90: #e2e2e2,
    92: #e8e8e8,
    94: #eeeeee,
    95: #f1f1f1,
    96: #f4f4f4,
    98: #f9f9f9,
    99: #fcfcfc,
    100: #ffffff,
  ),
  neutral-variant: (
    0: #000000,
    10: #221919,
    20: #382e2e,
    25: #443938,
    30: #4f4444,
    35: #5b504f,
    40: #685b5b,
    50: #817473,
    60: #9c8d8d,
    70: #b7a8a7,
    80: #d3c3c2,
    90: #f0dfdd,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);
$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

// Estilos específicos de componentes
.mat-app-background {
  // Mantener los estilos específicos del datepicker y otros componentes
  .mat-datepicker-content {
    background-color: var(--color-background) !important;
    color: var(--color-text-primary) !important;

    .mat-calendar {
      background-color: transparent;

      .mat-calendar-body-cell-content {
        color: var(--color-text-primary);
      }

      .mat-calendar-body-selected {
        background-color: var(--color-primary);
        color: white;
      }

      .mat-calendar-body-today:not(.mat-calendar-body-selected) {
        border-color: var(--color-primary);
      }

      .mat-calendar-arrow,
      .mat-calendar-previous-button,
      .mat-calendar-next-button {
        color: var(--color-text-secondary);
      }
    }
  }

  // Asegurar que el overlay container esté por encima de todo
  .cdk-overlay-container {
    z-index: 1000;
  }
}

// Estilos mínimos necesarios para el datepicker de Material
.mat-datepicker-content {
  background-color: rgb(var(--surface-color)) !important;
  color: var(--text-color) !important;
  border-radius: 8px !important;
  box-shadow: var(--card-shadow) !important;
  backdrop-filter: var(--card-backdrop-filter);
  min-width: 280px !important;
  max-width: 320px !important;
  margin: 0 auto;

  .mat-calendar {
    background-color: transparent;

    // Header styling
    .mat-calendar-header {
      padding: 0.5rem 0.5rem 0;

      .mat-calendar-controls {
        margin: 0 0.5rem;

        .mat-calendar-period-button {
          color: var(--text-color);
          font-weight: 500;
          padding: 0.5rem;
          border-radius: 4px;

          &:hover {
            background-color: rgba(255, 255, 255, 0.05);
          }
        }

        .mat-calendar-arrow {
          fill: var(--text-color);
        }
      }
    }

    // Table styling
    .mat-calendar-table {
      padding: 0.5rem;

      .mat-calendar-table-header {
        color: var(--text-secondary);
        font-size: 12px;
        font-weight: 500;
        text-align: center;

        th {
          padding: 0.5rem 0;
        }
      }

      // Calendar cells
      .mat-calendar-body-cell {
        height: 36px;
        width: 36px;
        border-radius: 50%;

        &:not(.mat-calendar-body-disabled) {
          &:hover .mat-calendar-body-cell-content {
            background-color: rgba(255, 255, 255, 0.08);
          }
        }

        .mat-calendar-body-cell-content {
          height: 32px;
          width: 32px;
          border-radius: 50%;
          line-height: 32px;
          color: var(--text-color);
          border: none;

          &.mat-calendar-body-selected {
            background-color: var(--primary-color);
            color: white;
          }

          &.mat-calendar-body-today {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
          }
        }

        &.mat-calendar-body-disabled {
          > .mat-calendar-body-cell-content {
            color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    // Previous/Next navigation buttons
    .mat-calendar-previous-button,
    .mat-calendar-next-button {
      color: var(--text-color);

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &[disabled] {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

// Datepicker toggle button styling
.mat-datepicker-toggle {
  color: var(--text-secondary);

  .mat-datepicker-toggle-default-icon {
    width: 24px;
    height: 24px;
  }

  &:hover {
    .mat-datepicker-toggle-default-icon {
      color: var(--primary-color);
    }
  }
}

// Ensure proper overlay positioning
.cdk-overlay-container {
  .cdk-overlay-pane {
    &.mat-datepicker-popup {
      @media (max-width: 599px) {
        max-width: calc(100vw - 32px) !important;
        margin: 0 auto;
      }
    }
  }
}

// Estilos del calendario
.mat-calendar-body-cell-content {
  color: rgba(255, 255, 255, 0.87);
}

.mat-calendar-body-selected {
  background-color: #2196f3;
  color: white;
}

.mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: #2196f3;
}

.mat-calendar-table-header,
.mat-calendar-body-label {
  color: rgba(255, 255, 255, 0.7);
}

.mat-calendar-arrow {
  border-top-color: white;
}

.mat-calendar-next-button,
.mat-calendar-previous-button {
  color: white;
}

.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  background-color: rgba(255, 255, 255, 0.04);
}

// Ajustes de overlay y posicionamiento
.cdk-overlay-container {
  z-index: 1000;
}

.cdk-overlay-connected-position-bounding-box {
  position: absolute;
  z-index: 1000;
}

// Estilos para snackbars informativos
.info-snackbar {
  background-color: #3f51b5 !important; // Color primario indigo
  color: white !important;
  border-left: 4px solid #1a237e !important; // Borde izquierdo más oscuro
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;

  .mat-simple-snackbar {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .mat-simple-snackbar-action {
    color: #ff9800 !important; // Color naranja para el botón de acción
    font-weight: bold !important;

    button {
      background-color: rgba(255, 255, 255, 0.1) !important;
      border-radius: 4px !important;
      padding: 4px 12px !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
      }
    }
  }
}

// Ajustes para el contenedor de pestañas
.mat-tab-body-content {
  overflow: visible;
  height: auto;
}

.mat-tab-body.mat-tab-body-active {
  overflow: visible;
  position: relative;
  z-index: 1;

  .curriculum-container {
    overflow: visible;
  }
}

// ===== ESTILOS GLASSMORPHISM PARA TABLA PERSONALIZADA =====
// NOTA: Los estilos glassmorphism se aplican desde los componentes individuales
// Comentado temporalmente para permitir que los estilos glassmorphism tomen precedencia
/*
app-custom-table {
  .custom-table-container {
    background-color: var(--color-surface) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    overflow: hidden !important;
    border: 1px solid rgba(72, 72, 72, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
  }

  .custom-table {
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;

    th {
      background-color: var(--color-background-dark) !important;
      color: var(--color-text-primary) !important;
      font-weight: 600 !important;
      padding: 1.25rem 1rem !important;
      text-align: left !important;
      border-bottom: 2px solid rgba(25, 118, 210, 0.3) !important;
      position: sticky !important;
      top: 0 !important;
      z-index: 10 !important;
      font-size: 0.95rem !important;
      letter-spacing: 0.5px !important;
      text-transform: uppercase !important;
    }

    td {
      padding: 1.25rem 1rem !important;
      border-bottom: 1px solid rgba(72, 72, 72, 0.2) !important;
      color: var(--color-text-primary) !important;
      font-size: 0.95rem !important;
      transition: all 0.2s ease !important;
    }

    tbody tr {
      transition: all 0.3s ease !important;

      &:hover {
        background-color: var(--color-background-hover) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
      }
    }
  }

  .loading-overlay {
    background-color: rgba(51, 51, 51, 0.7) !important;

    .spinner {
      border: 3px solid rgba(25, 118, 210, 0.3) !important;
      border-top-color: var(--color-primary) !important;
    }
  }

  .pagination-container {
    background-color: var(--color-background-dark) !important;
    border-top: 1px solid var(--color-border) !important;

    .pagination-button, .page-button {
      color: var(--color-text-primary) !important;

      &:hover:not(:disabled) {
        color: var(--color-primary) !important;
        background-color: rgba(25, 118, 210, 0.1) !important;
      }

      &:disabled {
        color: var(--color-text-disabled) !important;
      }
    }

    .page-button.active {
      background-color: var(--color-primary) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.4) !important;
    }
  }
}
*/

// Estilos para los badges de roles
.role-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.35rem 0.85rem !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.3px !important;
  background-color: rgba(25, 118, 210, 0.12) !important;
  color: var(--color-primary) !important;
  box-shadow: 0 2px 10px rgba(25, 118, 210, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  margin: 0.2rem !important;
}

// Estilos para los badges de estado
.estado-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.35rem 0.85rem !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.3px !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.estado-activo {
  background-color: rgba(76, 175, 80, 0.8) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.15) !important;
  border: 1px solid rgba(76, 175, 80, 0.2) !important;
}

.estado-inactivo {
  background-color: rgba(255, 152, 0, 0.8) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 10px rgba(255, 152, 0, 0.15) !important;
  border: 1px solid rgba(255, 152, 0, 0.2) !important;
}

.estado-bloqueado {
  background-color: rgba(244, 67, 54, 0.8) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 10px rgba(244, 67, 54, 0.15) !important;
  border: 1px solid rgba(244, 67, 54, 0.2) !important;
}

// Forzar texto blanco en badges de posición específicos
.badge-posicion.estado-bloqueado {
  background: #f44336 !important;
  color: white !important;

  span, i {
    color: white !important;
  }
}

.badge-posicion.estado-activo {
  background: #4caf50 !important;
  color: white !important;

  span, i {
    color: white !important;
  }
}

// Estilos para los enlaces de email
.email-link {
  color: var(--color-primary) !important;
  text-decoration: none !important;
  position: relative !important;
  padding: 0.1rem 0.2rem !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  display: inline-flex !important;
  align-items: center !important;
}

// Estilos para celdas de ID
.id-cell {
  font-family: monospace !important;
  font-size: 0.85rem !important;
  color: var(--color-text-secondary) !important;
  background-color: rgba(66, 66, 66, 0.5) !important;
  padding: 0.2rem 0.5rem !important;
  border-radius: 4px !important;
  display: inline-block !important;
}

// Estilos para información de usuario (SOLO PARA TABLAS - NO NAVBAR)
// NOTA: Estos estilos están scoped para no interferir con el navbar user-info
.custom-table .user-info,
.table-container .user-info,
app-custom-table .user-info {
  display: flex !important;
  flex-direction: column !important;

  .user-name {
    font-weight: 500 !important;
    color: var(--color-text-primary) !important;
  }

  .user-dni {
    font-size: 0.85rem !important;
    color: var(--color-text-secondary) !important;
    margin-top: 0.2rem !important;
  }
}

// Estilos para celdas de fecha
.date-info {
  display: flex !important;
  flex-direction: column !important;

  .date-value {
    font-weight: 500 !important;
    color: var(--color-text-primary) !important;
  }

  .date-time {
    font-size: 0.85rem !important;
    color: var(--color-text-secondary) !important;
    margin-top: 0.2rem !important;
  }
}

// Estilos para los botones de acciones
.actions-container {
  display: flex !important;
  gap: 0.5rem !important;
  justify-content: center !important;

  app-custom-button {
    ::ng-deep button {
      min-width: auto !important;
      width: 38px !important;
      height: 38px !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border-radius: 50% !important;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
      background-color: rgba(66, 66, 66, 0.8) !important;
      border: 1px solid rgba(72, 72, 72, 0.2) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
      position: relative !important;
      overflow: hidden !important;

      &:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 6px 15px rgba(25, 118, 210, 0.2) !important;
        border-color: rgba(25, 118, 210, 0.3) !important;
      }

      i {
        font-size: 1rem !important;
        color: var(--color-text-primary) !important;
        position: relative !important;
        z-index: 2 !important;
        transition: all 0.3s ease !important;
      }

      &:hover i {
        color: var(--color-primary) !important;
        transform: scale(1.1) !important;
      }
    }
  }
}

// Estilos para tema oscuro
@media (prefers-color-scheme: dark) {
  app-custom-table {
    .custom-table-container {
      background-color: var(--color-surface-dark) !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
      border: 1px solid rgba(51, 51, 51, 0.2) !important;
    }

    .custom-table {
      th {
        background-color: var(--color-background-dark) !important;
        color: var(--color-text-primary-dark) !important;
        border-bottom: 2px solid rgba(21, 101, 192, 0.3) !important;
      }

      td {
        border-bottom: 1px solid rgba(51, 51, 51, 0.2) !important;
        color: var(--color-text-primary-dark) !important;
      }

      tbody tr {
        &:hover {
          background-color: var(--color-background-hover-dark) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
        }
      }
    }

    .loading-overlay {
      background-color: rgba(34, 34, 34, 0.7) !important;

      .spinner {
        border-color: rgba(21, 101, 192, 0.3) !important;
        border-top-color: var(--color-primary-dark) !important;
      }
    }

    .pagination-container {
      background-color: var(--color-background-dark) !important;
      border-top: 1px solid var(--color-border-dark) !important;

      .pagination-button, .page-button {
        color: var(--color-text-primary-dark) !important;

        &:hover:not(:disabled) {
          color: var(--color-primary-dark) !important;
          background-color: rgba(21, 101, 192, 0.1) !important;
        }

        &:disabled {
          color: var(--color-text-disabled-dark) !important;
        }
      }

      .page-button.active {
        background-color: var(--color-primary-dark) !important;
        box-shadow: 0 2px 8px rgba(21, 101, 192, 0.4) !important;
      }
    }
  }

  .role-badge {
    background-color: rgba(21, 101, 192, 0.2) !important;
    color: var(--color-primary-dark) !important;
    box-shadow: 0 2px 10px rgba(21, 101, 192, 0.15) !important;
  }

  .estado-activo {
    background-color: rgba(56, 142, 60, 0.2) !important;
    color: var(--color-success-dark) !important;
    box-shadow: 0 2px 10px rgba(56, 142, 60, 0.15) !important;
    border: 1px solid rgba(56, 142, 60, 0.3) !important;
  }

  .estado-inactivo {
    background-color: rgba(245, 124, 0, 0.2) !important;
    color: var(--color-warning-dark) !important;
    box-shadow: 0 2px 10px rgba(245, 124, 0, 0.15) !important;
    border: 1px solid rgba(245, 124, 0, 0.3) !important;
  }

  .estado-bloqueado {
    background-color: rgba(211, 47, 47, 0.2) !important;
    color: var(--color-error-dark) !important;
    box-shadow: 0 2px 10px rgba(211, 47, 47, 0.15) !important;
    border: 1px solid rgba(211, 47, 47, 0.3) !important;
  }

  .email-link {
    color: var(--color-primary-dark) !important;
  }

  .id-cell {
    background-color: rgba(34, 34, 34, 0.5) !important;
    color: var(--color-text-secondary-dark) !important;
  }

  // Estilos específicos para user-info en tablas (NO NAVBAR)
  .custom-table .user-info,
  .table-container .user-info,
  app-custom-table .user-info {
    .user-name {
      color: var(--color-text-primary-dark) !important;
    }

    .user-dni {
      color: var(--color-text-secondary-dark) !important;
    }
  }

  .date-info {
    .date-value {
      color: var(--color-text-primary-dark) !important;
    }

    .date-time {
      color: var(--color-text-secondary-dark) !important;
    }
  }

  .actions-container {
    app-custom-button {
      ::ng-deep button {
        background-color: rgba(51, 51, 51, 0.8) !important;
        border: 1px solid rgba(51, 51, 51, 0.3) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

        i {
          color: var(--color-text-primary-dark) !important;
        }

        &:hover {
          box-shadow: 0 6px 15px rgba(21, 101, 192, 0.2) !important;
          border-color: rgba(21, 101, 192, 0.3) !important;

          i {
            color: var(--color-primary-dark) !important;
          }
        }
      }
    }
  }
}

// Estilos específicos para el contenedor del curriculum
.curriculum-container {
  padding: var(--spacing-xl);
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);

  .seccion {
    margin-bottom: var(--spacing-xl);

    &:last-child {
      margin-bottom: 0;
    }

    h2 {
      color: var(--color-primary);
      font-size: 1.25rem;
      font-weight: 500;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-sm);
      border-bottom: 2px solid rgba(var(--color-primary-rgb), 0.1);
    }
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .campo {
    margin-bottom: var(--spacing-lg);

    .etiqueta {
      color: var(--color-text-secondary);
      font-size: 0.875rem;
      margin-bottom: var(--spacing-xs);
    }

    .valor {
      color: var(--color-text-primary);
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .estado-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    font-weight: 500;

    &.pendiente {
      background-color: var(--badge-orange);
      color: var(--color-warning);
    }

    &.aprobado {
      background-color: var(--badge-green);
      color: var(--color-success);
    }

    &.rechazado {
      background-color: var(--badge-red);
      color: var(--color-danger);
    }
  }

  .documentos {
    .documento {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      background-color: var(--glass-background-light);
      border-radius: var(--border-radius-sm);
      margin-bottom: var(--spacing-sm);

      .icono {
        margin-right: var(--spacing-md);
        color: var(--color-primary);
      }

      .nombre {
        flex: 1;
        color: var(--color-text-primary);
      }

      .acciones {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }
}

// Estilos para el botón de cerrar
.cerrar-btn {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  padding: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);

  &:hover {
    color: var(--color-primary);
  }
}

// Estilos para los form fields
.mat-mdc-form-field {
    margin: 20px 0;

    .mat-mdc-form-field-flex {
        background-color: var(--glass-background-light) !important;
        padding: 0.5rem 1rem !important;
    }

    .mat-mdc-text-field-wrapper {
        background-color: transparent !important;
        padding: 0;
    }

    // Eliminar el outline por defecto de Material
    .mdc-notched-outline {
        display: none !important;
    }

    // Agregar línea inferior personalizada
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: rgba(var(--color-text-secondary-rgb), 0.3);
        transition: background-color 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    // Estilo del input
    .mat-mdc-input-element {
        color: var(--color-text-primary) !important;
        font-size: 0.875rem;
        padding: 15px 0;
        background: transparent;
        border: none;
    }

    // Estilo del label
    .mat-mdc-form-field-label {
        position: absolute;
        left: 0;
        color: var(--color-text-secondary) !important;
        pointer-events: none;
        font-size: 0.875rem;

        span {
            display: inline-block;
            min-width: 5px;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
    }

    // Estados focus y con contenido
    &.mat-focused,
    &.mat-form-field-should-float {
        &::after {
            background-color: var(--color-primary);
        }

        .mat-mdc-form-field-label {
            color: var(--color-primary) !important;

            span {
                transform: translateY(-25px);
                @for $i from 0 through 20 {
                    &:nth-child(#{$i}) {
                        transition-delay: #{$i * 50}ms;
                    }
                }
            }
        }
    }
}

// ===== GLASSMORPHISM STYLES FOR SELECT PANELS =====
.mat-mdc-select-panel {
  background: rgba(55, 65, 81, 0.95) !important;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-radius: 8px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;

  .mat-mdc-option {
    color: #f9fafb !important;
    background: transparent !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover:not(.mat-mdc-option-disabled) {
      background: rgba(75, 85, 99, 0.6) !important;
      color: #f9fafb !important;
    }

    &.mat-mdc-option-selected {
      background: rgba(59, 130, 246, 0.8) !important;
      color: #f9fafb !important;
    }

    &.mat-mdc-option-active {
      background: rgba(59, 130, 246, 0.6) !important;
      color: #f9fafb !important;
    }
  }
}

// ===== GLASSMORPHISM FOR NATIVE SELECT DROPDOWNS =====
select {
  background: linear-gradient(135deg,
    rgba(75, 85, 99, 0.9) 0%,
    rgba(55, 65, 81, 0.9) 100%) !important;
  color: #f9fafb !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;

  &:focus {
    background: linear-gradient(135deg,
      rgba(75, 85, 99, 0.9) 0%,
      rgba(55, 65, 81, 0.9) 100%) !important;
    color: #f9fafb !important;
    outline: none !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  }

  &:hover {
    background: linear-gradient(135deg,
      rgba(75, 85, 99, 1) 0%,
      rgba(55, 65, 81, 1) 100%) !important;
    color: #f9fafb !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
  }

  option {
    background: rgba(55, 65, 81, 0.95) !important;
    color: #f9fafb !important;
    padding: 0.5rem !important;
  }
}

// ===== SPECIFIC GLASSMORPHISM FOR PAGINATION SELECTS =====
.pagination-container select,
.pagination-size select,
app-custom-table select {
  background: linear-gradient(135deg,
    rgba(75, 85, 99, 0.9) 0%,
    rgba(55, 65, 81, 0.9) 100%) !important;
  color: #f9fafb !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;

  &:focus,
  &:active,
  &:target {
    background: linear-gradient(135deg,
      rgba(75, 85, 99, 0.9) 0%,
      rgba(55, 65, 81, 0.9) 100%) !important;
    color: #f9fafb !important;
    outline: none !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  }

  &:hover {
    background: linear-gradient(135deg,
      rgba(75, 85, 99, 1) 0%,
      rgba(55, 65, 81, 1) 100%) !important;
    color: #f9fafb !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
  }
}

// Variables para el datepicker
$datepicker-bg: #424242;
$datepicker-text: rgba(255, 255, 255, 0.87);
$datepicker-selected: #2196f3;
$datepicker-hover: rgba(255, 255, 255, 0.04);
$datepicker-cell-size: 36px;

::ng-deep {
  // Overlay container principal
  .cdk-overlay-container {
    position: fixed !important;
    z-index: 10000 !important;
    pointer-events: none !important;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    isolation: isolate !important;

    // Panel del overlay
    .cdk-overlay-pane {
      pointer-events: auto !important;
      position: absolute !important;
      z-index: 11000 !important;
      background: none !important;
      isolation: isolate !important;

      // Excepción para paneles de autocompletado
      &:not(.mat-mdc-autocomplete-panel-above):not(.mat-mdc-autocomplete-panel-below) {
        max-width: 320px !important;
        min-width: 280px !important;
      }
    }

    // Backdrop
    .cdk-overlay-backdrop {
      background: rgba(0, 0, 0, 0.32) !important;
      backdrop-filter: blur(2px) !important;
      z-index: 10500 !important;
    }
  }

  // Popup del datepicker
  .mat-datepicker-popup {
    background: $datepicker-bg !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    width: 300px !important;
    max-width: 320px !important;
    min-width: 280px !important;
    margin: 8px !important;
    padding: 0 !important;
    overflow: hidden !important;
    isolation: isolate !important;

    .mat-datepicker-content {
      background: $datepicker-bg !important;
      color: $datepicker-text !important;
      display: block !important;
      padding: 0 !important;
      margin: 0 !important;
      border-radius: 8px !important;
      overflow: hidden !important;
    }
  }

  // Calendario
  .mat-calendar {
    width: 100% !important;
    background: $datepicker-bg !important;
    color: $datepicker-text !important;
    padding: 8px !important;

    // Header del calendario
    &-header {
      padding: 8px 8px 0 8px !important;
      background: none !important;

      .mat-calendar-controls {
        margin: 0 !important;

        .mat-calendar-period-button,
        .mat-calendar-previous-button,
        .mat-calendar-next-button {
          color: $datepicker-text !important;

          &:hover {
            background: $datepicker-hover !important;
          }
        }
      }
    }

    // Tabla del calendario
    &-table {
      width: 100% !important;
      border-spacing: 0 !important;
      border-collapse: collapse !important;

      th {
        height: 32px !important;
        text-align: center !important;
        font-weight: 500 !important;
        color: $datepicker-text !important;
      }
    }

    // Celdas del calendario
    &-body-cell {
      position: relative !important;
      height: $datepicker-cell-size !important;
      padding: 0 !important;
      text-align: center !important;
      outline: none !important;
      cursor: pointer !important;

      &-content {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: $datepicker-cell-size !important;
        height: $datepicker-cell-size !important;
        line-height: $datepicker-cell-size !important;
        border-radius: 50% !important;
        font-size: 13px !important;
        color: $datepicker-text !important;

        &:hover {
          background: $datepicker-hover !important;
        }
      }
    }

    // Estados especiales
    &-body-selected {
      background: $datepicker-selected !important;
      color: white !important;

      &:hover {
        background: darken($datepicker-selected, 5%) !important;
      }
    }

    &-body-today:not(.mat-calendar-body-selected) {
      border: 1px solid $datepicker-selected !important;
    }

    &-body-disabled {
      opacity: 0.5 !important;
      cursor: default !important;

      .mat-calendar-body-cell-content {
        cursor: default !important;

        &:hover {
          background: none !important;
        }
      }
    }
  }
}

// Asegurar que los contenedores no interfieran
.app-root,
main.mat-app-background,
.perfil-container,
.mat-tab-body-content,
.mat-tab-body-active,
.mat-tab-body-wrapper {
  overflow: visible !important;
  position: relative !important;
  z-index: auto !important;
}

// Asegurar que el sidebar y otros elementos no interfieran
.sidebar,
.header {
  z-index: 5 !important;
}

// Estilos para scrollbars
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-color-rgb), 0.5) rgba(var(--surface-color-rgb), 0.3);

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(var(--surface-color-rgb), 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-color-rgb), 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(var(--primary-color-rgb), 0.7);
    }
  }
}

// Estilos específicos para componentes de Material
.mat-mdc-tab-body-content,
.mat-dialog-content,
.mat-mdc-select-panel,
.mat-mdc-autocomplete-panel,
.mat-mdc-menu-panel,
.mat-expansion-panel-content,
.mat-drawer-content,
.mat-sidenav-content,
.mat-drawer-container,
.mat-table-container,
.mat-mdc-table,
.mat-mdc-paginator,
.mat-mdc-card-content,
.mat-mdc-list,
.cdk-virtual-scroll-viewport,
.mat-calendar-content {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(var(--surface-color-rgb), 0.3) !important;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-color-rgb), 0.5) !important;
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(var(--primary-color-rgb), 0.7) !important;
    }
  }

  scrollbar-width: thin !important;
  scrollbar-color: rgba(var(--primary-color-rgb), 0.5) rgba(var(--surface-color-rgb), 0.3) !important;
}

// Contenedores con scroll personalizado
.scroll-container,
.scrollable-content,
.scroll-area,
[scrollable="true"],
[data-scrollable="true"],
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-color-rgb), 0.5) rgba(var(--surface-color-rgb), 0.3);

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(var(--surface-color-rgb), 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-color-rgb), 0.5);
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(var(--primary-color-rgb), 0.7);
    }
  }
}

// Sobrescribir estilos de Material para los contenedores con scroll
.mat-mdc-dialog-container,
.mat-mdc-select-panel,
.mat-mdc-menu-content,
.mat-mdc-table-container {
  max-height: 80vh !important;
  overflow: auto !important;
}

// Estilos específicos para paneles de autocompletado
.mat-mdc-autocomplete-panel {
  max-height: 300px !important;
  overflow: auto !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  z-index: 1001 !important;
}

// Asegurar que el panel de autocompletado se muestre correctamente
.mat-autocomplete-panel-above,
.mat-autocomplete-panel-below {
  width: 100% !important;
  max-width: 100% !important;
}

// Estilos para diálogos
.dark-backdrop {
  background: rgba(0, 0, 0, 0.7);
}

.cdk-overlay-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cdk-overlay-backdrop {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9998;
  pointer-events: auto;
  transition: opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);
  opacity: 0;

  &.cdk-overlay-backdrop-showing {
    opacity: 1;
  }
}

.cdk-global-overlay-wrapper {
  display: flex;
  position: absolute;
  z-index: 9999;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cdk-overlay-pane {
  position: absolute;
  pointer-events: auto;
  box-sizing: border-box;
  z-index: 9999;
}

// Variables para Snackbars (mantener estas definiciones en un solo lugar)
$snackbar-success: rgba(76, 175, 80, 0.95);
$snackbar-error: rgba(244, 67, 54, 0.95);
$snackbar-warning: rgba(255, 193, 7, 0.95);
$snackbar-info: rgba(33, 150, 243, 0.95);
$snackbar-text: #ffffff;
$snackbar-action: rgba(255, 255, 255, 0.9);

// Unificar el mixin de snackbar
@mixin snackbar-base($bg-color) {
  .mdc-snackbar__surface {
    background-color: $bg-color !important;
    color: $snackbar-text !important;
    min-height: 48px !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    align-items: center !important;
  }

  .mdc-snackbar__label {
    color: $snackbar-text !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    letter-spacing: 0.25px !important;
    padding: 0 !important;

    &::before {
      font-family: 'Font Awesome 5 Free' !important;
      font-weight: 900 !important;
      margin-right: 8px !important;
    }
  }

  .mat-mdc-snack-bar-actions {
    padding-inline-start: 16px !important;
    margin-inline-end: -8px !important;

    button {
      color: $snackbar-action !important;
      font-size: 13px !important;
      font-weight: 500 !important;
      text-transform: uppercase !important;
      letter-spacing: 0.5px !important;
      padding: 0 12px !important;
      height: 32px !important;
      border-radius: 4px !important;
      background: rgba(255, 255, 255, 0.1) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.2) !important;
      }
    }
  }
}

// Estilos unificados para todos los tipos de snackbar
.success-snackbar {
  @include snackbar-base($snackbar-success);
  .mdc-snackbar__label::before { content: '\f058'; }
}

.error-snackbar {
  @include snackbar-base($snackbar-error);
  .mdc-snackbar__label::before { content: '\f057'; }
}

.warning-snackbar {
  @include snackbar-base($snackbar-warning);
  .mdc-snackbar__label::before { content: '\f071'; }
}

.info-snackbar {
  @include snackbar-base($snackbar-info);
  .mdc-snackbar__label::before { content: '\f05a'; }
}

// Alias para mantener compatibilidad con código existente
.severe-snackbar { @extend .error-snackbar; }
.clipboard-warning { @extend .warning-snackbar; }

// Animación unificada para snackbars
@keyframes snackbarSlideIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Contenedor de snackbar
.mat-mdc-snack-bar-container {
  animation: snackbarSlideIn 0.3s ease-out;
  margin: 16px !important;
}

// Estilos para diálogos
.mat-mdc-dialog-container {
  --mdc-dialog-container-color: var(--background-color);
  --mdc-dialog-with-divider-divider-color: var(--card-border);
}

.mat-mdc-dialog-surface {
  border-radius: 8px !important;
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  box-shadow: var(--card-shadow) !important;
}

// Estilos específicos para el diálogo de acuse
.acknowledge-dialog {
    .mat-mdc-dialog-container {
        --mdc-dialog-container-color: var(--background-color);
        padding: 0;
        border-radius: 8px;
        overflow: hidden;

        .mat-mdc-dialog-content {
            padding: 0;
            margin: 0;
            max-height: 80vh;
        }

        .mat-mdc-dialog-actions {
            padding: 16px 24px;
            margin: 0;
            border-top: 1px solid var(--card-border);
        }
    }
}

// ===== GLASSMORPHISM DIALOG STYLES FOR USER DETAILS =====
// Extensión del sistema glassmorphism existente para custom-dialog

// Aplicar variables del sistema a custom-dialog
app-custom-dialog .dialog-backdrop {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
}

app-custom-dialog .dialog-container {
  background: var(--background-color) !important;
  border: 1px solid var(--card-border) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 16px !important;
  box-shadow: var(--card-shadow) !important;
  color: var(--text-color) !important;
  min-width: 600px !important;
  max-width: 800px !important;
}

// Header del diálogo usando variables del sistema
app-custom-dialog .dialog-header {
  background: transparent !important;
  border-bottom: 1px solid var(--card-border) !important;
  padding: 1.5rem 2rem !important;
}

app-custom-dialog .dialog-title {
  color: var(--text-color) !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

app-custom-dialog .dialog-icon {
  color: var(--primary-color) !important;
}

app-custom-dialog .close-button {
  background: rgba(239, 68, 68, 0.15) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
  border-radius: 6px !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .close-button:hover {
  background: rgba(239, 68, 68, 0.25) !important;
  border-color: rgba(239, 68, 68, 0.5) !important;
  transform: translateY(-1px) !important;
}

// Contenido del diálogo usando variables del sistema
app-custom-dialog .dialog-content {
  padding: 2rem !important;
  background: transparent !important;
  overflow-y: auto !important;
  max-height: 70vh !important;
}

// Elementos específicos del usuario-detalle usando variables del sistema
app-custom-dialog .user-header {
  background: var(--background-color) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 8px !important;
  box-shadow: var(--card-shadow) !important;
  color: var(--text-color) !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
}

app-custom-dialog .user-header:hover {
  border-color: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-1px) !important;
}

// Elementos del usuario usando variables del sistema
app-custom-dialog .user-avatar i {
  color: var(--primary-color) !important;
}

app-custom-dialog .user-info h2 {
  color: var(--text-color) !important;
  font-weight: 600 !important;
}

app-custom-dialog .user-dni {
  color: var(--text-secondary) !important;
}

// Estados del usuario usando variables del sistema
app-custom-dialog .user-status {
  backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 0.375rem 0.75rem !important;
  border: 1px solid transparent !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

// Estados semánticos (mantenemos colores específicos para semántica)
app-custom-dialog .user-status.estado-activo {
  background: rgba(76, 175, 80, 0.15) !important;
  color: #4CAF50 !important;
  border-color: rgba(76, 175, 80, 0.3) !important;
}

app-custom-dialog .user-status.estado-inactivo {
  background: rgba(245, 158, 11, 0.15) !important;
  color: #f59e0b !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

app-custom-dialog .user-status.estado-bloqueado {
  background: rgba(239, 68, 68, 0.15) !important;
  color: #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

// Tarjetas personalizadas usando variables del sistema
app-custom-dialog app-custom-card .custom-card {
  background: var(--background-color) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 8px !important;
  box-shadow: var(--card-shadow) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-bottom: 1.5rem !important;
}

app-custom-dialog app-custom-card .custom-card:hover {
  border-color: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-2px) !important;
}

// Headers y contenido de tarjetas usando variables del sistema
app-custom-dialog app-custom-card .card-header {
  background: transparent !important;
  border-bottom: 1px solid var(--card-border) !important;
  padding: 1.25rem 1.5rem !important;
}

app-custom-dialog app-custom-card .card-title {
  color: var(--text-color) !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

app-custom-dialog app-custom-card .card-title i {
  color: var(--primary-color) !important;
  font-size: 1.25rem !important;
}

app-custom-dialog app-custom-card .card-content {
  background: transparent !important;
  padding: 1.5rem !important;
}

// Items de detalle usando variables del sistema
app-custom-dialog .detail-item {
  margin-bottom: 1rem !important;
  padding: 0.75rem 0 !important;
  border-bottom: 1px solid var(--card-border) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .detail-item:last-child {
  margin-bottom: 0 !important;
  border-bottom: none !important;
}

app-custom-dialog .detail-item:hover {
  background: rgba(255, 255, 255, 0.02) !important;
  border-radius: 4px !important;
  padding: 0.75rem !important;
  margin: 0 -0.75rem 1rem !important;
}

app-custom-dialog .detail-label {
  color: var(--text-secondary) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: inline-block !important;
  min-width: 80px !important;
  margin-right: 0.75rem !important;
}

app-custom-dialog .detail-value {
  color: var(--text-color) !important;
  font-weight: 500 !important;
}

app-custom-dialog .detail-value a {
  color: var(--primary-color) !important;
  text-decoration: none !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 4px !important;
  margin: -0.25rem -0.5rem !important;
}

app-custom-dialog .detail-value a:hover {
  background: rgba(59, 130, 246, 0.1) !important;
  text-decoration: underline !important;
  transform: translateY(-1px) !important;
}

app-custom-dialog .no-data {
  color: #9ca3af !important;
  opacity: 0.8 !important;
  font-style: italic !important;
}

// Badges de roles usando variables del sistema y colores semánticos
app-custom-dialog .role-badge {
  backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 8px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 0.375rem 0.875rem !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: inline-block !important;
  margin: 0.25rem !important;

  // Color por defecto (USER)
  background: rgba(59, 130, 246, 0.15) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: #3b82f6 !important;
}

app-custom-dialog .role-badge:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

// Colores semánticos para roles (mantenemos para semántica)
app-custom-dialog .role-badge[data-role*="admin"],
app-custom-dialog .role-badge[data-role*="ADMIN"] {
  background: rgba(239, 68, 68, 0.15) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

app-custom-dialog .role-badge[data-role*="moderator"],
app-custom-dialog .role-badge[data-role*="MODERATOR"] {
  background: rgba(76, 175, 80, 0.15) !important;
  border-color: rgba(76, 175, 80, 0.3) !important;
  color: #4CAF50 !important;
}

app-custom-dialog .role-badge[data-role*="editor"],
app-custom-dialog .role-badge[data-role*="EDITOR"] {
  background: rgba(245, 158, 11, 0.15) !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
  color: #f59e0b !important;
}

app-custom-dialog .role-badge[data-role*="viewer"],
app-custom-dialog .role-badge[data-role*="VIEWER"] {
  background: rgba(6, 182, 212, 0.15) !important;
  border-color: rgba(6, 182, 212, 0.3) !important;
  color: #06b6d4 !important;
}

// Elementos adicionales usando variables del sistema
app-custom-dialog .roles-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  padding: 0.5rem 0 !important;
}

app-custom-dialog .user-actions app-custom-button .custom-button {
  background: rgba(76, 175, 80, 0.15) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border: 1px solid rgba(76, 175, 80, 0.3) !important;
  border-radius: 8px !important;
  color: #4CAF50 !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .user-actions app-custom-button .custom-button:hover {
  background: rgba(76, 175, 80, 0.25) !important;
  border-color: rgba(76, 175, 80, 0.5) !important;
  transform: translateY(-1px) !important;
  color: #66BB6A !important;
}

app-custom-dialog .loading-container {
  background: var(--background-color) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 8px !important;
  box-shadow: var(--card-shadow) !important;
  padding: 2rem !important;
  text-align: center !important;
}

app-custom-dialog .loading-container p {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

app-custom-dialog .error-message {
  background: rgba(239, 68, 68, 0.15) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  text-align: center !important;
  margin-bottom: 1rem !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .error-message:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
  transform: translateY(-1px) !important;
}

// ===== GLASSMORPHISM STYLES FOR USER EDIT FORM =====
// Extensión del sistema glassmorphism para formulario de edición de usuario

// Contenedor principal del diálogo de usuario
app-custom-dialog .custom-dialog-container {
  background: var(--background-color) !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  border: 1px solid var(--card-border) !important;
  box-shadow: var(--card-shadow) !important;
}

// Formulario principal usando variables del sistema
app-custom-dialog .user-form {
  background: transparent !important;
  color: var(--text-color) !important;
  font-family: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
}

// Secciones del formulario
app-custom-dialog .form-section {
  background: var(--background-color) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 8px !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  box-shadow: var(--card-shadow) !important;
  margin-bottom: 1.5rem !important;
  padding: 1.5rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .form-section:hover {
  border-color: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-1px) !important;
}

// Headers de sección
app-custom-dialog .form-section h3 {
  color: var(--text-color) !important;
  border-bottom: 2px solid var(--primary-color) !important;
  margin: 0 0 1rem 0 !important;
  padding-bottom: 0.5rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

app-custom-dialog .form-section h3::before {
  color: var(--primary-color) !important;
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 900 !important;
  font-size: 0.8rem !important;
}

// Filas del formulario
app-custom-dialog .form-row {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 1rem !important;
  margin-bottom: 1rem !important;
}

// Campos de formulario personalizados
app-custom-dialog app-custom-form-field,
app-custom-dialog app-custom-select {
  flex: 1 !important;
  min-width: 250px !important;
}

// Contenedores de campos de entrada
app-custom-dialog app-custom-form-field .form-field-container,
app-custom-dialog app-custom-select .custom-select-container,
app-custom-dialog app-custom-form-field input,
app-custom-dialog app-custom-form-field textarea {
  background: rgba(75, 85, 99, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: var(--text-color) !important;
}

app-custom-dialog app-custom-form-field .form-field-container:hover,
app-custom-dialog app-custom-select .custom-select-container:hover {
  border-color: rgba(255, 255, 255, 0.3) !important;
  background: rgba(75, 85, 99, 0.4) !important;
}

app-custom-dialog app-custom-form-field .form-field-container:focus-within,
app-custom-dialog app-custom-select .custom-select-container:focus-within {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  background: rgba(75, 85, 99, 0.5) !important;
}

// Labels de campos
app-custom-dialog app-custom-form-field label,
app-custom-dialog app-custom-select label {
  color: var(--text-secondary) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
}

// Inputs y selects
app-custom-dialog app-custom-form-field input,
app-custom-dialog app-custom-form-field textarea,
app-custom-dialog app-custom-select select {
  background: transparent !important;
  color: var(--text-color) !important;
  border: none !important;
  outline: none !important;
  width: 100% !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
}

app-custom-dialog app-custom-form-field input::placeholder,
app-custom-dialog app-custom-form-field textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
  font-style: italic !important;
}

// Mejoras específicas para los inputs
app-custom-dialog app-custom-form-field input,
app-custom-dialog app-custom-form-field textarea {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.75rem !important;
}

// Mejoras para los labels
app-custom-dialog app-custom-form-field .form-label,
app-custom-dialog app-custom-select .form-label {
  color: var(--text-secondary) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

// Estilos para campos requeridos
app-custom-dialog app-custom-form-field[required] .form-label::after,
app-custom-dialog app-custom-select[required] .form-label::after {
  content: ' *' !important;
  color: #ef4444 !important;
  font-weight: bold !important;
}

// Contenedor de roles (checkboxes)
app-custom-dialog .roles-container {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: 0.75rem !important;
  margin-top: 1rem !important;
}

app-custom-dialog .role-checkbox {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  border-radius: 6px !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  -webkit-backdrop-filter: var(--card-backdrop-filter) !important;
  padding: 0.75rem !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
}

app-custom-dialog .role-checkbox:hover {
  background: rgba(59, 130, 246, 0.15) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  transform: translateY(-1px) !important;
}

app-custom-dialog .role-checkbox.checked {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
}

app-custom-dialog .role-checkbox label {
  color: var(--text-color) !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

app-custom-dialog .role-checkbox input[type="checkbox"] {
  accent-color: var(--primary-color) !important;
  width: 16px !important;
  height: 16px !important;
  margin: 0 !important;
}

// Mejoras específicas para los componentes custom
app-custom-dialog app-custom-checkbox {
  width: 100% !important;
}

app-custom-dialog app-custom-checkbox .checkbox-container {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

app-custom-dialog app-custom-checkbox .checkbox-container label {
  color: var(--text-color) !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  cursor: pointer !important;
  padding: 0.5rem !important;
}

app-custom-dialog app-custom-checkbox input[type="checkbox"] {
  accent-color: var(--primary-color) !important;
  width: 18px !important;
  height: 18px !important;
  margin: 0 !important;
  border-radius: 3px !important;
}

// Botones de acción del formulario
app-custom-dialog .form-actions {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 1rem !important;
  margin-top: 2rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid var(--card-border) !important;
}

// Botón cancelar
app-custom-dialog .form-actions app-custom-button[color="accent"] .custom-button {
  background: rgba(156, 163, 175, 0.15) !important;
  border: 1px solid rgba(156, 163, 175, 0.3) !important;
  color: #9ca3af !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 6px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

app-custom-dialog .form-actions app-custom-button[color="accent"] .custom-button:hover {
  background: rgba(156, 163, 175, 0.25) !important;
  border-color: rgba(156, 163, 175, 0.5) !important;
  transform: translateY(-1px) !important;
}

// Botón guardar/crear
app-custom-dialog .form-actions app-custom-button[color="primary"] .custom-button,
app-custom-dialog .form-actions app-custom-button[color="primary"] button {
  background: rgba(76, 175, 80, 0.15) !important;
  border: 1px solid rgba(76, 175, 80, 0.3) !important;
  color: #4CAF50 !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 6px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-width: 140px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

app-custom-dialog .form-actions app-custom-button[color="primary"] .custom-button:hover,
app-custom-dialog .form-actions app-custom-button[color="primary"] button:hover {
  background: rgba(76, 175, 80, 0.25) !important;
  border-color: rgba(76, 175, 80, 0.5) !important;
  transform: translateY(-1px) !important;
  color: #66BB6A !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2) !important;
}

// Botón cancelar mejorado
app-custom-dialog .form-actions app-custom-button[color="accent"] .custom-button,
app-custom-dialog .form-actions app-custom-button[color="accent"] button {
  background: rgba(156, 163, 175, 0.15) !important;
  border: 1px solid rgba(156, 163, 175, 0.3) !important;
  color: #9ca3af !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
  border-radius: 6px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-width: 140px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

app-custom-dialog .form-actions app-custom-button[color="accent"] .custom-button:hover,
app-custom-dialog .form-actions app-custom-button[color="accent"] button:hover {
  background: rgba(156, 163, 175, 0.25) !important;
  border-color: rgba(156, 163, 175, 0.5) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(156, 163, 175, 0.2) !important;
}

// Estados de validación
app-custom-dialog app-custom-form-field.ng-invalid.ng-touched .form-field-container {
  border-color: rgba(239, 68, 68, 0.6) !important;
  background: rgba(239, 68, 68, 0.1) !important;
}

app-custom-dialog app-custom-form-field.ng-valid.ng-touched .form-field-container {
  border-color: rgba(34, 197, 94, 0.6) !important;
  background: rgba(34, 197, 94, 0.1) !important;
}

// Mensajes de error
app-custom-dialog app-form-error .error-message {
  background: rgba(239, 68, 68, 0.15) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
  border-radius: 4px !important;
  padding: 0.5rem 0.75rem !important;
  margin-top: 0.5rem !important;
  font-size: 0.75rem !important;
  backdrop-filter: var(--card-backdrop-filter) !important;
}

// Diseño responsivo para formulario de usuario
@media (max-width: 768px) {
  app-custom-dialog .form-row {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  app-custom-dialog app-custom-form-field,
  app-custom-dialog app-custom-select {
    min-width: unset !important;
  }

  app-custom-dialog .roles-container {
    grid-template-columns: 1fr !important;
    gap: 0.5rem !important;
  }

  app-custom-dialog .form-actions {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  app-custom-dialog .form-actions app-custom-button {
    width: 100% !important;
  }

  app-custom-dialog .form-section {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }
}

// Estados de carga
app-custom-dialog .form-actions app-custom-button[loading="true"] .custom-button {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

// Mejoras de accesibilidad
app-custom-dialog .user-form:focus-within {
  outline: none !important;
}

app-custom-dialog .form-section:focus-within {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

// Estados disabled
app-custom-dialog app-custom-form-field[disabled] .form-field-container,
app-custom-dialog app-custom-select[disabled] .custom-select-container {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  background: rgba(75, 85, 99, 0.2) !important;
}

app-custom-dialog app-custom-form-field[disabled] input,
app-custom-dialog app-custom-form-field[disabled] textarea,
app-custom-dialog app-custom-select[disabled] select {
  cursor: not-allowed !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

// Estilo para snackbar persistente durante penalización
.persistent-snackbar {
  .mdc-snackbar__surface {
    border-left: 4px solid $snackbar-error !important;
  }

  .mdc-snackbar__label {
    color: $snackbar-text !important;
    font-weight: 500 !important;
  }

  .mat-mdc-snack-bar-actions button {
    color: $snackbar-text !important;
    font-weight: 500 !important;
  }
}

// ===== ESTILOS GLASSMORPHISM PARA DIÁLOGOS =====
// Estilos con mayor especificidad para sobrescribir estilos del componente
.unified-dialog-container.glassmorphism-dialog .dialog-backdrop,
.unified-dialog-container.concurso-form-dialog-container .dialog-backdrop {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  z-index: 1050;
}

.unified-dialog-container.glassmorphism-dialog .dialog-container,
.unified-dialog-container.concurso-form-dialog-container .dialog-container {
  // Fondo glassmorphism premium
  background: rgba(55, 65, 81, 0.85);
  background-image: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(76, 175, 80, 0.08) 30%,
    rgba(255, 255, 255, 0.12) 70%,
    rgba(76, 175, 80, 0.06) 100%);

  // Bordes y efectos
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;

  // Sombras premium
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.5),
    0 10px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);

  // Estructura y layout optimizado
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 500px;
  max-width: 90vw;
  min-height: 400px;
  max-height: 90vh;
  width: auto;
  height: auto; // Altura automática basada en contenido

  // Efecto de brillo superior
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent);
    z-index: 10;
  }

  // Tamaños específicos con layout consistente
  &.small {
    min-width: 400px;
    max-width: 500px;
    width: 85vw;
    height: 70vh;
    min-height: 400px;
  }

  &.large {
    min-width: min(600px, 95vw); // Responsive min-width
    max-width: 95vw;
    width: clamp(320px, 80vw, 1200px); // Mobile-first approach
    height: auto;
    max-height: 90vh;
    min-height: min(500px, 80vh); // Responsive min-height

    @media (max-width: 768px) {
      min-width: 95vw;
      width: 95vw;
      min-height: min(400px, 70vh);
    }

    @media (max-width: 480px) {
      min-width: 100vw;
      width: 100vw;
      max-width: 100vw;
      min-height: min(350px, 60vh);
      border-radius: 0; // Sin bordes redondeados en móviles muy pequeños
    }
  }

  &.fullscreen {
    width: 95vw;
    height: 95vh;
    max-width: none;
    max-height: none;
    min-width: 95vw;
    min-height: 95vh;

    @media (max-width: 768px) {
      width: 100vw;
      height: 100vh;
      min-width: 100vw;
      min-height: 100vh;
      border-radius: 0;
    }
  }
}

// Header del diálogo - Fijo (no scrolleable)
.unified-dialog-container.glassmorphism-dialog .dialog-header,
.unified-dialog-container.concurso-form-dialog-container .dialog-header {
  background: linear-gradient(135deg,
    rgba(76, 175, 80, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(76, 175, 80, 0.12) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding: 1.5rem 2rem;
  position: relative;
  z-index: 5;
  flex-shrink: 0; // No se reduce cuando hay poco espacio
  min-height: 80px; // Altura mínima garantizada

  .dialog-title {
    color: #f9fafb;
    font-weight: 600;
    font-size: 1.375rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .dialog-icon {
      color: #4CAF50;
      font-size: 1.25rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }
}

// Botón de cerrar
.unified-dialog-container.glassmorphism-dialog .close-button,
.unified-dialog-container.concurso-form-dialog-container .close-button,
.unified-dialog-container.glassmorphism-dialog app-custom-button[variant="icon"],
.unified-dialog-container.concurso-form-dialog-container app-custom-button[variant="icon"] {
  ::ng-deep .custom-button {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #d1d5db;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;

    &:hover {
      background: rgba(244, 67, 54, 0.8);
      border-color: rgba(244, 67, 54, 0.4);
      color: #f9fafb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
    }
  }
}

// Contenido del diálogo - Mejorado con scroll
.unified-dialog-container.glassmorphism-dialog .dialog-content,
.unified-dialog-container.concurso-form-dialog-container .dialog-content {
  padding: 0;
  background: transparent;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  position: relative;
  z-index: 2;
  max-height: calc(85vh - 140px); // Optimizado con el nuevo padding del footer
  min-height: 350px; // Altura mínima mejorada para mejor usabilidad

  // Estilos para la barra de scroll personalizada
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(76, 175, 80, 0.6);
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(76, 175, 80, 0.8);
    }
  }

  // Para Firefox
  scrollbar-width: thin;
  scrollbar-color: rgba(76, 175, 80, 0.6) rgba(255, 255, 255, 0.1);
}

// Estilos específicos para el formulario de concursos dentro del diálogo
.unified-dialog-container.glassmorphism-dialog .dialog-content,
.unified-dialog-container.concurso-form-dialog-container .dialog-content {

  // Contenedor del formulario
  app-concurso-form-dialog {
    display: block;
    padding: 1rem 2rem; // Reducir padding vertical para ganar espacio
    height: 100%; // Aprovechar toda la altura disponible

    // Contenedor de pestañas
    app-custom-tabs {
      display: block;
      height: 100%; // Aprovechar toda la altura

      .tabs-container {
        display: flex;
        flex-direction: column;
        height: 100%; // Altura completa
      }

      .tabs-content {
        padding: 0.75rem 0; // Reducir padding para ganar espacio
        flex: 1; // Tomar todo el espacio disponible
        overflow-y: auto; // Permitir scroll en el contenido de las pestañas

        // Espaciado entre campos del formulario - Optimizado
        .form-row {
          margin-bottom: 1.25rem; // Reducir espaciado para ganar espacio

          &:last-child {
            margin-bottom: 1rem; // Reducir espacio final
          }
        }

        // Campos del formulario - Espaciado optimizado
        app-custom-form-field {
          margin-bottom: 1rem; // Reducir espaciado
          display: block;
        }

        // Campos de texto y select - Espaciado optimizado
        app-custom-select,
        app-custom-datepicker,
        app-custom-textarea {
          margin-bottom: 1rem; // Reducir espaciado
          display: block;
        }
      }
    }
  }
}

// Footer del diálogo - Fijo (no scrolleable)
.unified-dialog-container.glassmorphism-dialog .dialog-footer,
.unified-dialog-container.concurso-form-dialog-container .dialog-footer {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(76, 175, 80, 0.03) 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding: 1.25rem 2rem; // Reducir padding vertical para ganar espacio
  position: relative;
  z-index: 5;
  flex-shrink: 0; // No se reduce cuando hay poco espacio
  min-height: 70px; // Altura mínima optimizada

  // Efecto de brillo en el borde superior
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  }

  // Estilos para los botones del footer
  .dialog-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    align-items: center;

    app-custom-button {
      ::ng-deep .custom-button {
        min-width: 120px;
        height: 44px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.2s ease;

        &[data-variant="stroked"] {
          background: rgba(55, 65, 81, 0.6);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: #d1d5db;
          backdrop-filter: blur(8px);

          &:hover {
            background: rgba(55, 65, 81, 0.8);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
          }
        }

        &[data-variant="flat"] {
          background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
          border: 1px solid rgba(76, 175, 80, 0.3);
          color: white;
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);

          &:hover {
            background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
            transform: translateY(-1px);
          }

          &:disabled {
            background: rgba(55, 65, 81, 0.4);
            color: rgba(255, 255, 255, 0.5);
            border-color: rgba(255, 255, 255, 0.1);
            box-shadow: none;
            transform: none;
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .unified-dialog-container.glassmorphism-dialog .dialog-container,
  .unified-dialog-container.concurso-form-dialog-container .dialog-container {
    min-width: 90vw;
    max-width: 95vw;
    width: 90vw;
    max-height: 90vh;

    &.large {
      min-width: 90vw;
      max-width: 95vw;
      width: 90vw;
      max-height: 90vh;
    }
  }

  .unified-dialog-container.glassmorphism-dialog .dialog-header,
  .unified-dialog-container.concurso-form-dialog-container .dialog-header {
    padding: 1.25rem 1.5rem;

    .dialog-title {
      font-size: 1.25rem;
    }
  }

  .unified-dialog-container.glassmorphism-dialog .dialog-footer,
  .unified-dialog-container.concurso-form-dialog-container .dialog-footer {
    padding: 1.25rem 1.5rem;

    &::before {
      left: 1.5rem;
      right: 1.5rem;
    }
  }
}

@media (max-width: 480px) {
  .unified-dialog-container.glassmorphism-dialog .dialog-container,
  .unified-dialog-container.concurso-form-dialog-container .dialog-container {
    min-width: 95vw;
    max-width: 98vw;

    &.large {
      min-width: 95vw;
      max-width: 98vw;
      width: 95vw;
    }
  }

  .unified-dialog-container.glassmorphism-dialog .dialog-header,
  .unified-dialog-container.concurso-form-dialog-container .dialog-header {
    padding: 1rem 1.25rem;
  }

  .unified-dialog-container.glassmorphism-dialog .dialog-footer,
  .unified-dialog-container.concurso-form-dialog-container .dialog-footer {
    padding: 1rem 1.25rem;

    &::before {
      left: 1.25rem;
      right: 1.25rem;
    }
  }
}

// Backdrop con blur para diálogos
.cdk-overlay-dark-backdrop {
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.mat-mdc-tab-group,
.mat-mdc-tab-nav-bar {
  --mat-tab-header-active-label-text-color: #03a9f4 !important;
  --mdc-tab-indicator-active-indicator-color: #03a9f4 !important;
}

.mat-mdc-tab-label.mat-mdc-tab-label-active,
.mat-mdc-tab-label.mat-mdc-tab-label-active:focus,
.mat-mdc-tab-label.mat-mdc-tab-label-active:active {
  color: #03a9f4 !important;
  font-weight: 800 !important;
}

/* ===== SOLUCIÓN ÓPTIMA: DROPDOWN OVERFLOW FIX GLOBAL ===== */
/* Permitir que los dropdowns se muestren correctamente en toda la aplicación */
/* Esta es la solución más simple y eficiente para el problema de dropdowns cortados */

app-custom-card:has(.filter-form),
app-custom-card:has(.filter-row),
app-custom-card .custom-card,
.filter-card,
.filter-container,
.search-container,
.form-container {
  overflow: visible !important;
}

/* Sobrescribir el overflow: hidden del CustomCardComponent para cards con filtros */
app-custom-card:has(.filter-form) .custom-card,
app-custom-card:has(.filter-row) .custom-card,
.filters-card .custom-card,
.filter-container .custom-card {
  overflow: visible !important;
}

app-custom-card:has(.filter-form) .card-content,
app-custom-card:has(.filter-row) .card-content,
.filters-card .card-content,
.filter-container .card-content {
  overflow: visible !important;
}

/* Z-index para dropdowns en filtros para asegurar que aparezcan encima */
.filter-form app-custom-select,
.filter-row app-custom-select,
.search-container app-custom-select {
  position: relative;
  z-index: 100;
}

/* Asegurar que los dropdowns de custom-select tengan z-index adecuado */
app-custom-select .select-dropdown {
  z-index: 9999999 !important;
  position: absolute !important;
}

/* Asegurar que el contenedor del select permita overflow */
app-custom-select .custom-select,
app-custom-select .custom-select-container {
  overflow: visible !important;
}

/* ===== APEXCHARTS GLOBAL STYLING ===== */
/* Global styling for ApexCharts components */

/* ApexCharts glassmorphism integration */
app-stats-chart {
  display: block;
  height: 100%;

  apx-chart {
    height: 100%;
  }
}

/* === DOCUMENTO VIEWER MODAL - FULL SIZE PDF === */
/* Solución basada en investigación de ng2-pdf-viewer issues y StackOverflow */
.documento-viewer-dialog {
  /* Forzar que el modal use dimensiones completas */
  .dialog-container {
    max-width: 95vw !important;
    width: 95vw !important;
    height: 95vh !important;
    max-height: 95vh !important;
    margin: 0 auto !important;
  }

  /* Asegurar que el contenido del modal sea flexible */
  .dialog-content {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 0 !important;
    overflow: hidden !important;
  }

  /* Contenedor del PDF a pantalla completa */
  .pdf-container {
    flex: 1 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: relative !important;
  }

  /* Estilos específicos para ng2-pdf-viewer - Solución de GitHub Issue #493 */
  pdf-viewer {
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }
}

/* === ESTILOS GLOBALES PARA NG2-PDF-VIEWER === */
/* Solución basada en StackOverflow y GitHub Issues */
.ng2-pdf-viewer-container {
  overflow: hidden !important;
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  background-color: #f5f5f5 !important;
}

.pdfViewer {
  padding: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.pdfViewer .page {
  margin: 10px auto !important;
  border: 1px solid #ccc !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* SOLUCIÓN ESPECÍFICA PARA ALTURA 0 - Basada en debugging */
.documento-viewer-dialog {
  /* Forzar altura en el contenedor del modal */
  .dialog-content {
    height: 80vh !important;
    min-height: 600px !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Forzar altura en el contenedor del PDF */
  .pdf-container {
    height: 100% !important;
    min-height: 500px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Forzar altura en pdf-viewer */
  ::ng-deep pdf-viewer {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    min-height: 500px !important;
    flex: 1 !important;
  }

  /* Forzar altura en ng2-pdf-viewer-container con scroll vertical */
  ::ng-deep .ng2-pdf-viewer-container {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    min-height: 500px !important;
    max-height: 70vh !important;
    background-color: #f5f5f5 !important;

    /* Scroll suave y estilos personalizados */
    scroll-behavior: smooth !important;
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
  }

  /* Estilos para la barra de scroll en navegadores WebKit */
  ::ng-deep .ng2-pdf-viewer-container::-webkit-scrollbar {
    width: 12px !important;
  }

  ::ng-deep .ng2-pdf-viewer-container::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 6px !important;
  }

  ::ng-deep .ng2-pdf-viewer-container::-webkit-scrollbar-thumb {
    background: #888 !important;
    border-radius: 6px !important;
    border: 2px solid #f1f1f1 !important;
  }

  ::ng-deep .ng2-pdf-viewer-container::-webkit-scrollbar-thumb:hover {
    background: #555 !important;
  }

  /* Forzar altura en pdfViewer con scroll optimizado */
  ::ng-deep .pdfViewer {
    padding: 20px !important;
    width: 100% !important;
    height: auto !important;
    min-height: 500px !important;
    display: block !important;
    overflow: visible !important;
  }

  /* Forzar dimensiones correctas en las páginas con scroll suave */
  ::ng-deep .pdfViewer .page {
    margin: 20px auto !important;
    border: 1px solid #ccc !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    max-width: 90% !important;
    width: auto !important;
    height: auto !important;
    min-height: 400px !important;
    display: block !important;
    visibility: visible !important;

    /* Transición suave para mejor experiencia */
    transition: box-shadow 0.2s ease !important;
  }

  /* Efecto hover en las páginas */
  ::ng-deep .pdfViewer .page:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
  }

  /* Forzar dimensiones en canvas */
  ::ng-deep .pdfViewer .page canvas {
    width: 100% !important;
    height: auto !important;
    min-height: 400px !important;
    max-width: 100% !important;
    display: block !important;
  }

  /* Asegurar que el contenido sea visible */
  ::ng-deep .pdfViewer > * {
    visibility: visible !important;
    opacity: 1 !important;
  }
}


