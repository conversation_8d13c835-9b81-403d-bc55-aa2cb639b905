version: '3.8'

services:
  # Nginx Proxy con SSL
  nginx-proxy:
    image: nginx:stable-alpine
    container_name: mpd-concursos-nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl-setup/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl-setup/certbot/conf:/etc/letsencrypt:ro
      - ./ssl-setup/certbot/www:/var/www/certbot:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - mpd-concursos-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Certbot para certificados SSL
  certbot:
    image: certbot/certbot:latest
    container_name: mpd-concursos-certbot
    volumes:
      - ./ssl-setup/certbot/conf:/etc/letsencrypt
      - ./ssl-setup/certbot/www:/var/www/certbot
      - ./ssl-setup/certbot/logs:/var/log/letsencrypt
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN}
    depends_on:
      - nginx-proxy
    networks:
      - mpd-concursos-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: mpd-concursos-mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root1234}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-mpd_concursos}
      MYSQL_USER: ${MYSQL_USER:-mpd_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-mpd_password}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    networks:
      - mpd-concursos-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend Spring Boot
  backend:
    build:
      context: ./concurso-backend
      dockerfile: Dockerfile
    container_name: mpd-concursos-backend-prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ***********************/${MYSQL_DATABASE:-mpd_concursos}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      SPRING_DATASOURCE_USERNAME: ${MYSQL_USER:-mpd_user}
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD:-mpd_password}
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_JPA_SHOW_SQL: "false"
      SPRING_SQL_INIT_MODE: never
      SPRING_MVC_CORS_ALLOWED_ORIGINS: https://${DOMAIN}
      JAVA_OPTS: "-Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
      SERVER_PORT: 8080
      LOGGING_LEVEL_ROOT: WARN
      LOGGING_LEVEL_AR_GOV_MPD: INFO
    volumes:
      - document_storage_prod:/app/document-storage
      - ./logs:/app/logs
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.5'
        reservations:
          memory: 512M
          cpus: '0.5'
    networks:
      - mpd-concursos-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Angular
  frontend:
    build:
      context: ./mpd-concursos-app-frontend
      dockerfile: Dockerfile
    container_name: mpd-concursos-frontend-prod
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    networks:
      - mpd-concursos-network
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mysql_data_prod:
    driver: local
  document_storage_prod:
    driver: local
  nginx_logs:
    driver: local

networks:
  mpd-concursos-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
