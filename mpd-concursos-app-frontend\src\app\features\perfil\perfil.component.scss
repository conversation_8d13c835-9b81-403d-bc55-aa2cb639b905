// Perfil Component Styles - Using Unified Glassmorphism System
@import 'src/styles/unified-glassmorphism-system';

:host {
  display: block;
  min-height: 100vh;
  background: transparent; // Let the dashboard container background show through
}

.perfil-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-md);
  min-height: calc(100vh - 4rem);
  background: transparent;

  @media (max-width: 768px) {
    padding: var(--spacing-md) var(--spacing-sm);
  }
}

// Header Section
.perfil-header {
  @include glassmorphism-card('primary');
  margin-bottom: var(--spacing-2xl);
  overflow: hidden;

  &:hover {
    transform: var(--transform-hover);
    box-shadow: var(--shadow-lg);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2xl);
    background: var(--glass-gradient-primary);
    color: var(--text-primary);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--glass-gradient-overlay);
      pointer-events: none;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      text-align: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 1;

      i {
        font-size: 2.5rem;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
        letter-spacing: -0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

        @media (max-width: 768px) {
          font-size: 1.8rem;
        }
      }
    }

    .header-actions {
      position: relative;
      z-index: 1;

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        padding: 0.75rem 1.25rem;
        border-radius: 25px;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        i {
          opacity: 0.8;
        }
      }
    }
  }
}

// Loading State
.loading-container {
  @include glassmorphism-card('primary');
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl) var(--spacing-2xl);
  text-align: center;

  app-custom-spinner {
    margin-bottom: var(--spacing-xl);
  }

  p {
    margin: 0;
    color: var(--text-tertiary);
    font-size: 1.1rem;
    font-weight: 500;
  }
}

// Main Content
.perfil-content {
  @include glassmorphism-card('primary');
  overflow: hidden;

  &:hover {
    transform: var(--transform-hover);
    box-shadow: var(--shadow-lg);
  }
}

// Tab Content Wrapper
.tab-content-wrapper {
  min-height: 600px;
  padding: var(--spacing-2xl);

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
}

// Section Styles
.personal-info-section,
.cv-section,
.documentation-section,
.linkedin-section {
  animation: glassSlideUp var(--duration-slow) var(--timing-ease);
}

// Modals
.modals-container {
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: var(--backdrop-filter-medium);
    animation: glassFadeIn var(--duration-normal) var(--timing-ease);

    .modal-container {
      @include glassmorphism-dialog;
      max-width: 90vw;
      max-height: 90vh;
      overflow: auto;
      animation: glassSlideUp var(--duration-slow) var(--timing-ease);
    }
  }
}

// Animations are now handled by the unified glassmorphism system
// See glassmorphism-system.scss for glassFadeIn, glassSlideUp, and glassScaleIn

/* ===== PERFIL TABS REDESIGN - MÁXIMA CLARIDAD Y VISIBILIDAD ===== */
/* Rediseño completo para eliminar conflictos de especificidad */

:host ::ng-deep {
  app-custom-tabs {
    /* Container principal */
    .custom-tabs-container {
      background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(51, 65, 85, 0.9) 100%) !important;
      border: 1px solid rgba(148, 163, 184, 0.2) !important;
      border-radius: 16px !important;
      backdrop-filter: blur(20px) !important;
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.3),
        0 10px 10px -5px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
      overflow: hidden !important;
    }

    /* Header de pestañas */
    .tabs-header {
      background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.98) 0%,
        rgba(30, 41, 59, 0.95) 100%) !important;
      backdrop-filter: blur(16px) !important;
      border-bottom: 1px solid rgba(148, 163, 184, 0.15) !important;
      padding: 8px !important;
      border-radius: 16px 16px 0 0 !important;
      position: relative !important;

      /* Efecto de brillo superior */
      &::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 1px !important;
        background: linear-gradient(90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent) !important;
      }
    }

    /* Navegación de pestañas */
    .tabs-nav {
      display: flex !important;
      position: relative !important;
      gap: 4px !important;
      padding: 0 !important;
    }

    /* BOTONES DE PESTAÑA - REDISEÑO COMPLETO */
    .tab-button {
      flex: 1 !important;
      padding: 16px 24px !important;
      background: transparent !important;
      border: none !important;
      border-radius: 12px !important;
      cursor: pointer !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      gap: 10px !important;
      position: relative !important;
      min-height: 60px !important;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;

      /* ESTADO INACTIVO - Texto gris claro */
      color: #94a3b8 !important;
      font-size: 15px !important;
      font-weight: 500 !important;

      /* Iconos en estado inactivo */
      i {
        font-size: 18px !important;
        opacity: 0.7 !important;
        color: #94a3b8 !important;
        transition: all 0.3s ease !important;
        margin-right: 8px !important;
      }

      /* Texto en estado inactivo */
      span {
        color: #94a3b8 !important;
        font-size: 15px !important;
        font-weight: 500 !important;
      }

      /* HOVER para pestañas inactivas */
      &:hover:not(.active):not(.disabled) {
        background: rgba(148, 163, 184, 0.1) !important;
        color: #e2e8f0 !important;
        transform: translateY(-1px) !important;

        i {
          opacity: 1 !important;
          color: #60a5fa !important;
          transform: scale(1.05) !important;
        }

        span {
          color: #e2e8f0 !important;
        }
      }

      /* ESTADO ACTIVO - MÁXIMA VISIBILIDAD */
      &.active {
        /* Fondo distintivo para pestaña activa */
        background: linear-gradient(135deg,
          rgba(59, 130, 246, 0.25) 0%,
          rgba(37, 99, 235, 0.2) 100%) !important;

        /* TEXTO BLANCO PURO - FORZADO CON MÁXIMA ESPECIFICIDAD */
        color: #ffffff !important;
        font-weight: 700 !important;
        font-size: 15px !important;

        /* Borde distintivo */
        border: 1px solid rgba(59, 130, 246, 0.4) !important;
        box-shadow:
          0 8px 25px rgba(59, 130, 246, 0.15),
          0 4px 10px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;

        /* Iconos en estado activo */
        i {
          color: #ffffff !important;
          opacity: 1 !important;
          font-size: 18px !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
          margin-right: 8px !important;
        }

        /* Texto en estado activo */
        span {
          color: #ffffff !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
          font-size: 15px !important;
          font-weight: 700 !important;
        }

        /* Forzar blanco en TODOS los elementos hijos */
        * {
          color: #ffffff !important;
        }

        /* Indicador inferior distintivo */
        &::after {
          content: '' !important;
          position: absolute !important;
          bottom: -1px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          width: 60% !important;
          height: 3px !important;
          background: linear-gradient(90deg, #3b82f6, #60a5fa) !important;
          border-radius: 2px !important;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.6) !important;
        }
      }

      /* Estado deshabilitado */
      &.disabled {
        color: #64748b !important;
        cursor: not-allowed !important;
        opacity: 0.4 !important;

        i {
          opacity: 0.3 !important;
          color: #64748b !important;
        }

        span {
          color: #64748b !important;
        }
      }

      /* Focus para accesibilidad */
      &:focus-visible {
        outline: 2px solid #60a5fa !important;
        outline-offset: 2px !important;
        z-index: 10 !important;
      }
    }

    /* Contenido de pestañas */
    .tabs-content {
      background: transparent !important;
      padding: 0 !important;
      border: none !important;
    }

    .tab-content {
      background: transparent !important;
      padding: 0 !important;
      border: none !important;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .perfil-container {
    padding: var(--spacing-xl) var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .perfil-container {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .perfil-header .header-content {
    padding: var(--spacing-xl);
  }

  .tab-content-wrapper {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .perfil-header .header-content .header-title h1 {
    font-size: 1.5rem;
  }

  .modals-container .modal-overlay .modal-container {
    margin: var(--spacing-md);
    max-width: calc(100vw - var(--spacing-2xl));
  }
}

/* ===== SOBRESCRITURA ADICIONAL PARA MÁXIMA ESPECIFICIDAD ===== */
/* Estilos adicionales para garantizar que el texto blanco se aplique */

:host ::ng-deep {
  app-custom-tabs {
    /* Sobrescritura de variables CSS globales */
    --color-primary-dark: #94a3b8 !important;
    --color-primary: #94a3b8 !important;
    --text-primary: #94a3b8 !important;
    --user-text-primary: #94a3b8 !important;

    /* Forzar estilos específicos para pestañas activas */
    .tab-button.active,
    button.tab-button.active,
    .tabs-nav .tab-button.active,
    .tabs-header .tab-button.active {
      /* Variables CSS específicas para pestaña activa */
      --color-primary-dark: #ffffff !important;
      --color-primary: #ffffff !important;
      --text-primary: #ffffff !important;
      --user-text-primary: #ffffff !important;

      /* Texto blanco forzado con máxima especificidad */
      color: #ffffff !important;

      /* Forzar blanco en todos los elementos hijos */
      i, span, *,
      .fas, .fa,
      [class*="fa-"] {
        color: #ffffff !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
      }
    }

    /* Sobrescritura para elementos específicos que podrían tener estilos conflictivos */
    .tab-button.active i.fas,
    .tab-button.active i.fa,
    .tab-button.active span,
    .tab-button.active [class*="fa-"] {
      color: #ffffff !important;
      opacity: 1 !important;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      .tab-button {
        &.active {
          background: rgba(96, 165, 250, 0.4) !important;
          color: #ffffff !important;
          border: 2px solid #60a5fa !important;
          font-weight: 700 !important;

          &::after {
            height: 4px !important;
            background: #60a5fa !important;
            box-shadow: 0 0 8px #60a5fa !important;
          }
        }

        &:hover:not(.active) {
          background: rgba(255, 255, 255, 0.15) !important;
          color: #ffffff !important;
        }
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .tab-button {
        transition: none !important;
        transform: none !important;

        &:hover {
          transform: none !important;
        }
      }
    }
  }
}

// Accessibility and print styles are handled by the unified glassmorphism system
// See glassmorphism-system.scss for prefers-reduced-motion, prefers-contrast, and print styles


