// Perfil Component Styles - Using Unified Glassmorphism System
@import 'src/styles/unified-glassmorphism-system';

:host {
  display: block;
  min-height: 100vh;
  background: transparent; // Let the dashboard container background show through
}

.perfil-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-md);
  min-height: calc(100vh - 4rem);
  background: transparent;

  @media (max-width: 768px) {
    padding: var(--spacing-md) var(--spacing-sm);
  }
}

// Header Section
.perfil-header {
  @include glassmorphism-card('primary');
  margin-bottom: var(--spacing-2xl);
  overflow: hidden;

  &:hover {
    transform: var(--transform-hover);
    box-shadow: var(--shadow-lg);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2xl);
    background: var(--glass-gradient-primary);
    color: var(--text-primary);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--glass-gradient-overlay);
      pointer-events: none;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      text-align: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 1;

      i {
        font-size: 2.5rem;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
        letter-spacing: -0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

        @media (max-width: 768px) {
          font-size: 1.8rem;
        }
      }
    }

    .header-actions {
      position: relative;
      z-index: 1;

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        padding: 0.75rem 1.25rem;
        border-radius: 25px;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        i {
          opacity: 0.8;
        }
      }
    }
  }
}

// Loading State
.loading-container {
  @include glassmorphism-card('primary');
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl) var(--spacing-2xl);
  text-align: center;

  app-custom-spinner {
    margin-bottom: var(--spacing-xl);
  }

  p {
    margin: 0;
    color: var(--text-tertiary);
    font-size: 1.1rem;
    font-weight: 500;
  }
}

// Main Content
.perfil-content {
  @include glassmorphism-card('primary');
  overflow: hidden;

  &:hover {
    transform: var(--transform-hover);
    box-shadow: var(--shadow-lg);
  }
}

// Tab Content Wrapper
.tab-content-wrapper {
  min-height: 600px;
  padding: var(--spacing-2xl);

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
}

// Section Styles
.personal-info-section,
.cv-section,
.documentation-section,
.linkedin-section {
  animation: glassSlideUp var(--duration-slow) var(--timing-ease);
}

// Modals
.modals-container {
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: var(--backdrop-filter-medium);
    animation: glassFadeIn var(--duration-normal) var(--timing-ease);

    .modal-container {
      @include glassmorphism-dialog;
      max-width: 90vw;
      max-height: 90vh;
      overflow: auto;
      animation: glassSlideUp var(--duration-slow) var(--timing-ease);
    }
  }
}

// Animations are now handled by the unified glassmorphism system
// See glassmorphism-system.scss for glassFadeIn, glassSlideUp, and glassScaleIn

// Custom Tabs Styling Override
:host ::ng-deep {
  app-custom-tabs {
    .tabs-container {
      background: transparent;
      border-bottom: 2px solid var(--border-primary);
      margin-bottom: 0;
    }

    .tabs-header {
      background: transparent;
      border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
      padding: 0;
      gap: 0;
      display: flex;
      width: 100%;
      overflow: visible;
    }

    .tab-header {
      background: transparent;
      border-radius: 12px 12px 0 0;
      padding: 0;
      gap: 0;
      display: flex;
      width: 100%;
      overflow: visible;

      .tab-button {
        flex: 1;
        padding: var(--spacing-md) var(--spacing-2xl);
        border: none;
        background: transparent;
        color: var(--user-text-tab-inactive, var(--text-tertiary));
        font-weight: 500;
        font-size: var(--user-font-size-tab, 1rem); /* Use accessibility-improved font size */
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        transition: var(--transition-normal);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 0;
        min-height: 52px; /* Improved touch target size */

        &:hover {
          background: rgba(255, 255, 255, 0.08); /* Improved hover visibility */
          color: var(--user-text-primary, var(--text-primary));
          transform: var(--transform-hover);
          box-shadow: var(--shadow-md);
        }

        &.active {
          background: rgba(96, 165, 250, 0.15); /* Enhanced active state background */
          color: #ffffff !important; /* FORCE WHITE - Maximum contrast for active tab */
          font-weight: 600;
          box-shadow: var(--shadow-md), var(--shadow-inset);
          border: 1px solid var(--user-border-tab-active, var(--border-primary));
          border-bottom: 2px solid var(--user-border-tab-active, var(--border-primary)); /* Enhanced bottom border */

          /* Force white color on all child elements */
          i, span, * {
            color: #ffffff !important;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px; /* Thicker indicator for better visibility */
            background: linear-gradient(90deg, var(--user-text-accent, #60a5fa), #60a5fa);
            box-shadow: 0 -2px 8px rgba(96, 165, 250, 0.4);
          }
        }

        i {
          margin-right: var(--spacing-sm);
          font-size: 1.1rem;
        }

        @media (max-width: 768px) {
          padding: var(--spacing-md) var(--spacing-md);
          font-size: var(--user-font-size-tab-mobile, 0.9375rem); /* Improved mobile font size */
          min-height: 48px; /* Better touch targets on mobile */

          .tab-label {
            display: none;
          }

          i {
            margin-right: 0;
            font-size: 1.2rem; /* Larger icons for better visibility */
          }
        }
      }
    }

    .tab-content {
      background: transparent;
      padding: 0;
      border: none;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .perfil-container {
    padding: var(--spacing-xl) var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .perfil-container {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .perfil-header .header-content {
    padding: var(--spacing-xl);
  }

  .tab-content-wrapper {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .perfil-header .header-content .header-title h1 {
    font-size: 1.5rem;
  }

  .modals-container .modal-overlay .modal-container {
    margin: var(--spacing-md);
    max-width: calc(100vw - var(--spacing-2xl));
  }
}

// Enhanced accessibility support for profile tabs
:host ::ng-deep {
  app-custom-tabs {
    /* High contrast mode support */
    @media (prefers-contrast: high) {
      .tab-button {
        &.active {
          background: rgba(96, 165, 250, 0.3) !important;
          color: #ffffff !important;
          border: 2px solid #60a5fa !important;
          font-weight: 700 !important;

          &::after {
            height: 4px !important;
            background: #60a5fa !important;
            box-shadow: 0 0 8px #60a5fa !important;
          }
        }

        &:hover:not(.active) {
          background: rgba(255, 255, 255, 0.15) !important;
          color: #ffffff !important;
        }
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .tab-button {
        transition: none !important;
        transform: none !important;

        &:hover {
          transform: none !important;
        }
      }
    }
  }
}

// Accessibility and print styles are handled by the unified glassmorphism system
// See glassmorphism-system.scss for prefers-reduced-motion, prefers-contrast, and print styles


