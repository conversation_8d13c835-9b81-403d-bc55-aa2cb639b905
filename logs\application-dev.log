2025-07-24 15:41:31 [main] INFO  a.g.m.c.ConcursoBackendApplication - Starting ConcursoBackendApplication v0.0.1 using Java 21.0.7 with PID 1 (/app/app.jar started by root in /app)
2025-07-24 15:41:31 [main] DEBUG a.g.m.c.ConcursoBackendApplication - Running with Spring Boot v3.2.4, Spring v6.1.5
2025-07-24 15:41:31 [main] INFO  a.g.m.c.ConcursoBackendApplication - The following 1 profile is active: "dev"
2025-07-24 15:41:35 [main] INFO  a.g.m.c.auth.domain.jwt.JwtProvider - Clave JWT inicializada con HMAC-SHA256
2025-07-24 15:41:35 [main] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Filter 'jwtTokenFilter' configured for use
2025-07-24 15:41:35 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-07-24 15:41:42 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table answers (
        id binary(16) not null,
        attempts integer,
        hash varchar(255),
        question_id binary(16),
        response TEXT,
        response_time_ms bigint,
        status enum ('DRAFT','SUBMITTED','VALIDATED','SUSPICIOUS','INVALIDATED'),
        timestamp datetime(6),
        session_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table contest_dates (
        id bigint not null auto_increment,
        end_date date,
        label varchar(255),
        start_date date,
        type varchar(255),
        contest_id bigint,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table contest_requirements (
        id bigint not null auto_increment,
        category varchar(100) not null,
        created_at datetime(6),
        description varchar(500) not null,
        document_type varchar(100),
        priority integer not null,
        required bit not null,
        updated_at datetime(6),
        contest_id bigint not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table contests (
        id bigint not null auto_increment,
        bases_url varchar(255),
        category varchar(255),
        class_ varchar(255),
        department varchar(255),
        description_url varchar(255),
        end_date date,
        functions varchar(255),
        position varchar(255),
        start_date date,
        status enum ('DRAFT','PUBLISHED','PAUSED','CLOSED','FINISHED','CANCELLED','ARCHIVED'),
        title varchar(255),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table document_types (
        id binary(16) not null,
        code varchar(255),
        description varchar(255),
        is_active bit,
        name varchar(255) not null,
        `order` integer,
        required bit not null,
        parent_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table documents (
        id binary(16) not null,
        comments varchar(255),
        content_type varchar(255) not null,
        file_name varchar(255) not null,
        file_path varchar(255) not null,
        rejection_reason varchar(255),
        status enum ('PENDING','APPROVED','REJECTED') not null,
        upload_date datetime(6) not null,
        user_id binary(16) not null,
        validated_at datetime(6),
        validated_by binary(16),
        document_type_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table education (
        id binary(16) not null,
        activity_role varchar(255),
        activity_type varchar(255),
        average float(53),
        comments varchar(255),
        document_url varchar(255),
        duration_years integer,
        exposition_place_date varchar(255),
        had_final_evaluation bit,
        hourly_load integer,
        institution varchar(255) not null,
        issue_date date,
        status varchar(255) not null,
        thesis_topic varchar(255),
        title varchar(255) not null,
        topic varchar(255),
        type varchar(255) not null,
        user_id binary(16) not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:43 [main] DEBUG org.hibernate.SQL - 
    create table examination_allowed_materials (
        examination_id binary(16) not null,
        material varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table examination_requirements (
        examination_id binary(16) not null,
        requirement varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table examination_rules (
        examination_id binary(16) not null,
        rule varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table examination_security_violations (
        examination_id binary(16) not null,
        violation varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table examination_sessions (
        id binary(16) not null,
        current_question_index integer,
        deadline datetime(6),
        examination_id binary(16),
        start_time datetime(6),
        status enum ('CREATED','IN_PROGRESS','PAUSED','FINISHED','INVALIDATED'),
        user_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table examinations (
        id binary(16) not null,
        answers TEXT,
        cancellation_date datetime(6),
        cancellation_reason varchar(255),
        description varchar(255),
        duration_minutes bigint,
        end_time datetime(6),
        start_time datetime(6),
        status enum ('DRAFT','PUBLISHED','IN_PROGRESS','COMPLETED','CANCELLED','EXPIRED'),
        title varchar(255),
        type enum ('TECHNICAL_LEGAL','TECHNICAL_ADMINISTRATIVE','PSYCHOLOGICAL'),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table experience (
        id binary(16) not null,
        comments varchar(255),
        company varchar(255) not null,
        description varchar(255),
        document_url varchar(255),
        end_date date,
        position varchar(255) not null,
        start_date date not null,
        user_id binary(16) not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table inscription_circunscripciones (
        inscription_id BINARY(16) not null,
        circunscripcion varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table inscription_notes (
        id BINARY(16) not null,
        created_at datetime(6) not null,
        created_by BINARY(16),
        created_by_username varchar(255) not null,
        inscription_id BINARY(16) not null,
        text varchar(1000) not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table inscription_sessions (
        id BINARY(16) not null,
        contest_id bigint,
        created_at datetime(6),
        current_step enum ('INITIAL','TERMS_ACCEPTANCE','LOCATION_SELECTION','DOCUMENTATION','DATA_CONFIRMATION','COMPLETED'),
        expires_at datetime(6),
        form_data LONGTEXT,
        inscription_id BINARY(16),
        updated_at datetime(6),
        user_id BINARY(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table inscriptions (
        id BINARY(16) not null,
        accepted_terms bit,
        centro_de_vida varchar(255),
        confirmed_personal_data bit,
        contest_id bigint,
        created_at datetime(6),
        current_step enum ('INITIAL','TERMS_ACCEPTANCE','LOCATION_SELECTION','DOCUMENTATION','DATA_CONFIRMATION','COMPLETED'),
        data_confirmation_date datetime(6),
        documentation_deadline datetime(6),
        frozen_date datetime(6),
        inscription_date datetime(6),
        status enum ('ACTIVE','PENDING','COMPLETED_WITH_DOCS','COMPLETED_PENDING_DOCS','FROZEN','APPROVED','REJECTED','CANCELLED'),
        terms_acceptance_date datetime(6),
        updated_at datetime(6),
        user_id BINARY(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table notifications (
        id binary(16) not null,
        acknowledged_at datetime(6),
        acknowledgement_level enum ('NONE','SIMPLE','SIGNATURE_BASIC','SIGNATURE_ADVANCED') not null,
        content TEXT not null,
        read_at datetime(6),
        recipient_id binary(16) not null,
        sent_at datetime(6) not null,
        signature_metadata varchar(255),
        signature_type enum ('PIN','DIGITAL_CERT','BIOMETRIC','DECLARATION'),
        signature_value varchar(255),
        status enum ('PENDING','SENT','READ','ACKNOWLEDGED') not null,
        subject varchar(255) not null,
        type enum ('INSCRIPTION','SYSTEM','CONTEST','GENERAL') not null,
        version bigint,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table options (
        id binary(16) not null,
        order_number integer,
        text varchar(255),
        question_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table question_correct_answers (
        question_entity_id binary(16) not null,
        correct_answers varchar(255)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table question_options (
        id binary(16) not null,
        is_correct bit,
        text text,
        question_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table questions (
        id binary(16) not null,
        correct_answer varchar(255),
        order_number integer,
        score integer,
        text varchar(255),
        type enum ('SINGLE_CHOICE','MULTIPLE_CHOICE','TEXT','TRUE_FALSE'),
        examination_id binary(16),
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table roles (
        id binary(16) not null,
        name enum ('ROLE_ADMIN','ROLE_USER') not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table user_roles (
        user_id binary(16) not null,
        role_id binary(16) not null,
        primary key (user_id, role_id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    create table user_entity (
        id binary(16) not null,
        birth_date date,
        country varchar(255),
        created_at datetime(6) not null,
        cuit varchar(255),
        direccion varchar(255),
        dni varchar(255) not null,
        email varchar(255) not null,
        first_name varchar(255) not null,
        last_name varchar(255) not null,
        legal_address varchar(255),
        municipality varchar(255),
        password varchar(255) not null,
        province varchar(255),
        residential_address varchar(255),
        status enum ('ACTIVE','INACTIVE','BLOCKED','LOCKED','EXPIRED') not null,
        telefono varchar(255),
        username varchar(255) not null,
        primary key (id)
    ) engine=InnoDB
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table document_types 
       drop index UK_38wlce45ecy6m472frk5um7t0
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table document_types 
       add constraint UK_38wlce45ecy6m472frk5um7t0 unique (code)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       drop index UK_qj0ywt0gqra7hhxaaimq8gje4
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       add constraint UK_qj0ywt0gqra7hhxaaimq8gje4 unique (cuit)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       drop index UK_954y33fqknr0qy4jiolrpjo7r
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       add constraint UK_954y33fqknr0qy4jiolrpjo7r unique (dni)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       drop index UK_4xad1enskw4j1t2866f7sodrx
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       add constraint UK_4xad1enskw4j1t2866f7sodrx unique (email)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       drop index UK_2jsk4eakd0rmvybo409wgwxuw
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table user_entity 
       add constraint UK_2jsk4eakd0rmvybo409wgwxuw unique (username)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table answers 
       add constraint FKosmkkphkqwn4a1r8xkrbv3xcu 
       foreign key (session_id) 
       references examination_sessions (id)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table contest_dates 
       add constraint FK27nd17cg91bx0w6dx18hp2s6f 
       foreign key (contest_id) 
       references contests (id)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table contest_requirements 
       add constraint FKqjwvjjsvo1opgjg5xwhm6veka 
       foreign key (contest_id) 
       references contests (id)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table document_types 
       add constraint FKgm7p43mofkw563uq57ly1w208 
       foreign key (parent_id) 
       references document_types (id)
2025-07-24 15:41:44 [main] DEBUG org.hibernate.SQL - 
    alter table documents 
       add constraint FKis1i6nxslho3kvxr9nsg8x05l 
       foreign key (document_type_id) 
       references document_types (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table examination_allowed_materials 
       add constraint FKjggmksusbgut6y8hhxi81a7n7 
       foreign key (examination_id) 
       references examinations (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table examination_requirements 
       add constraint FK7xvasvkdhkkhjp4y7e6yp0lh5 
       foreign key (examination_id) 
       references examinations (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table examination_rules 
       add constraint FKqruutf1gwuifsavy4kjr5td2 
       foreign key (examination_id) 
       references examinations (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table examination_security_violations 
       add constraint FK8adigts8weqt2sixaisvcnnkh 
       foreign key (examination_id) 
       references examinations (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table experience 
       add constraint FKer0rwatwq7hfp6i4s2lk2l884 
       foreign key (user_id) 
       references user_entity (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table inscription_circunscripciones 
       add constraint FKcyoddmwjptv807llea6ysi2yi 
       foreign key (inscription_id) 
       references inscriptions (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table options 
       add constraint FK5bmv46so2y5igt9o9n9w4fh6y 
       foreign key (question_id) 
       references questions (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table question_correct_answers 
       add constraint FK4ut56al9gufoagxlplssgfaup 
       foreign key (question_entity_id) 
       references questions (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table question_options 
       add constraint FKsb9v00wdrgc9qojtjkv7e1gkp 
       foreign key (question_id) 
       references questions (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table questions 
       add constraint FK1y24yn7v3jwjp7le455y5ki10 
       foreign key (examination_id) 
       references examinations (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table user_roles 
       add constraint FKh8ciramu9cc9q3qcqiv4ue8a6 
       foreign key (role_id) 
       references roles (id)
2025-07-24 15:41:45 [main] DEBUG org.hibernate.SQL - 
    alter table user_roles 
       add constraint FK6y02653x6ebhsu2plf21ard62 
       foreign key (user_id) 
       references user_entity (id)
2025-07-24 15:41:46 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Using document storage location: './document-storage-dev'
2025-07-24 15:41:46 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Created document storage directory: ./document-storage-dev
2025-07-24 15:41:48 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 15:41:49 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 124 mappings in 'requestMappingHandlerMapping'
2025-07-24 15:41:49 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-24 15:41:49 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@736f0d1b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7fa3ea4f, org.springframework.security.web.context.SecurityContextHolderFilter@6c95bb3c, org.springframework.security.web.header.HeaderWriterFilter@1c8b1df1, org.springframework.web.filter.CorsFilter@382de41b, org.springframework.security.web.authentication.logout.LogoutFilter@58fd0074, ar.gov.mpd.concursobackend.auth.domain.jwt.JwtTokenFilter@13d984ee, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1688ac20, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@50836877, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@61805de3, org.springframework.security.web.session.SessionManagementFilter@7e811458, org.springframework.security.web.access.ExceptionTranslationFilter@251dd78f, org.springframework.security.web.access.intercept.AuthorizationFilter@545e2ebe]
2025-07-24 15:41:49 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-24 15:41:49 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 3 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-24 15:41:50 [main] INFO  a.g.m.c.ConcursoBackendApplication - Started ConcursoBackendApplication in 20.284 seconds (process running for 21.882)
2025-07-24 15:41:50 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Iniciando limpieza programada de entradas antiguas en la cola de documentos
2025-07-24 15:41:50 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Limpieza de cola de documentos completada
2025-07-24 15:41:50 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Iniciando proceso de congelación de inscripciones con plazo vencido
2025-07-24 15:41:50 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ie1_0.id,
        ie1_0.accepted_terms,
        ie1_0.centro_de_vida,
        ie1_0.confirmed_personal_data,
        ie1_0.contest_id,
        ie1_0.created_at,
        ie1_0.current_step,
        ie1_0.data_confirmation_date,
        ie1_0.documentation_deadline,
        ie1_0.frozen_date,
        ie1_0.inscription_date,
        ie1_0.status,
        ie1_0.terms_acceptance_date,
        ie1_0.updated_at,
        ie1_0.user_id 
    from
        inscriptions ie1_0 
    where
        ie1_0.status=? 
        and ie1_0.documentation_deadline<?
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificando roles básicos del sistema...
2025-07-24 15:41:50 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Encontradas 0 inscripciones con plazo de documentación vencido
2025-07-24 15:41:50 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Proceso de congelación completado. 0 inscripciones procesadas
2025-07-24 15:41:50 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Creando rol: ROLE_USER
2025-07-24 15:41:50 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        roles
        (name, id) 
    values
        (?, ?)
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Rol ROLE_USER creado con éxito
2025-07-24 15:41:50 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Creando rol: ROLE_ADMIN
2025-07-24 15:41:50 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        roles
        (name, id) 
    values
        (?, ?)
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Rol ROLE_ADMIN creado con éxito
2025-07-24 15:41:50 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificación de roles básicos completada.
2025-07-24 15:41:53 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@406f954c
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@64238d9a
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-24 15:41:53 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:41:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:41:53 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:41:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:41:58 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:03 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:08 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:08 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:13 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:13 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:18 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:18 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:23 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:28 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:28 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/8.11.0
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:48 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:48 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:42:59 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:42:59 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:43:29 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:43:29 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:43:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:43:59 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /api/auth/login
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /api/auth/login: true
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /api/auth/login
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /api/auth/login: true
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-07-24 15:44:00 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to ar.gov.mpd.concursobackend.auth.infrastructure.controller.AuthController#login(UserLogin, BindingResult)
2025-07-24 15:44:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [UserLogin(username=admin, password=admin123)]
2025-07-24 15:44:01 [http-nio-8080-exec-2] INFO  a.g.m.c.a.i.c.AuthController - Intento de login para usuario: admin
2025-07-24 15:44:01 [http-nio-8080-exec-2] INFO  a.g.m.c.a.a.service.UserService - Intentando autenticar al usuario: admin
2025-07-24 15:44:01 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        ue1_0.id 
    from
        user_entity ue1_0 
    where
        ue1_0.username=? 
    limit
        ?
2025-07-24 15:44:01 [http-nio-8080-exec-2] ERROR a.g.m.c.a.a.service.UserService - Usuario no encontrado: admin
2025-07-24 15:44:01 [http-nio-8080-exec-2] ERROR a.g.m.c.a.i.c.AuthController - Credenciales inválidas para usuario: admin
2025-07-24 15:44:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-07-24 15:44:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ar.gov.mpd.concursobackend.auth.infrastructure.controller.AuthController$ErrorResponse@14ebcec4]
2025-07-24 15:44:01 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 401 UNAUTHORIZED
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:44:29 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:44:29 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:44:59 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:44:59 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:45:29 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:45:29 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:45:59 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:45:59 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:46:55 [main] INFO  a.g.m.c.ConcursoBackendApplication - Starting ConcursoBackendApplication v0.0.1 using Java 21.0.7 with PID 1 (/app/app.jar started by root in /app)
2025-07-24 15:46:55 [main] DEBUG a.g.m.c.ConcursoBackendApplication - Running with Spring Boot v3.2.4, Spring v6.1.5
2025-07-24 15:46:55 [main] INFO  a.g.m.c.ConcursoBackendApplication - The following 1 profile is active: "dev"
2025-07-24 15:46:59 [main] INFO  a.g.m.c.auth.domain.jwt.JwtProvider - Clave JWT inicializada con HMAC-SHA256
2025-07-24 15:46:59 [main] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Filter 'jwtTokenFilter' configured for use
2025-07-24 15:46:59 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-07-24 15:47:00 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-24 15:47:03 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Using document storage location: './document-storage-dev'
2025-07-24 15:47:03 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Created document storage directory: ./document-storage-dev
2025-07-24 15:47:05 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 15:47:05 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 124 mappings in 'requestMappingHandlerMapping'
2025-07-24 15:47:05 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-24 15:47:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@64231d5d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2857f172, org.springframework.security.web.context.SecurityContextHolderFilter@4257255f, org.springframework.security.web.header.HeaderWriterFilter@2f83346f, org.springframework.web.filter.CorsFilter@43ff0650, org.springframework.security.web.authentication.logout.LogoutFilter@c4ac71b, ar.gov.mpd.concursobackend.auth.domain.jwt.JwtTokenFilter@4491eaeb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62cfb51a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@14676d59, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@482f0077, org.springframework.security.web.session.SessionManagementFilter@6295b7e2, org.springframework.security.web.access.ExceptionTranslationFilter@64922e2f, org.springframework.security.web.access.intercept.AuthorizationFilter@5d617472]
2025-07-24 15:47:06 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-24 15:47:06 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 3 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-24 15:47:06 [main] INFO  a.g.m.c.ConcursoBackendApplication - Started ConcursoBackendApplication in 12.238 seconds (process running for 13.326)
2025-07-24 15:47:06 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Iniciando limpieza programada de entradas antiguas en la cola de documentos
2025-07-24 15:47:06 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Limpieza de cola de documentos completada
2025-07-24 15:47:06 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Iniciando proceso de congelación de inscripciones con plazo vencido
2025-07-24 15:47:07 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificando roles básicos del sistema...
2025-07-24 15:47:07 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ie1_0.id,
        ie1_0.accepted_terms,
        ie1_0.centro_de_vida,
        ie1_0.confirmed_personal_data,
        ie1_0.contest_id,
        ie1_0.created_at,
        ie1_0.current_step,
        ie1_0.data_confirmation_date,
        ie1_0.documentation_deadline,
        ie1_0.frozen_date,
        ie1_0.inscription_date,
        ie1_0.status,
        ie1_0.terms_acceptance_date,
        ie1_0.updated_at,
        ie1_0.user_id 
    from
        inscriptions ie1_0 
    where
        ie1_0.status=? 
        and ie1_0.documentation_deadline<?
2025-07-24 15:47:07 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Encontradas 0 inscripciones con plazo de documentación vencido
2025-07-24 15:47:07 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Proceso de congelación completado. 0 inscripciones procesadas
2025-07-24 15:47:07 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:47:07 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_USER ya existe, no es necesario crearlo
2025-07-24 15:47:07 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:47:07 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_ADMIN ya existe, no es necesario crearlo
2025-07-24 15:47:07 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificación de roles básicos completada.
2025-07-24 15:47:08 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@70713e9d
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@793b6fb9
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-24 15:47:08 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:08 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:08 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:13 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:13 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:18 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:18 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:23 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:23 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:28 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:28 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:33 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:33 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:38 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:43 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:43 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:49 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:49 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:47:54 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:47:54 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:09 [main] INFO  a.g.m.c.ConcursoBackendApplication - Starting ConcursoBackendApplication v0.0.1 using Java 21.0.7 with PID 1 (/app/app.jar started by root in /app)
2025-07-24 15:48:09 [main] DEBUG a.g.m.c.ConcursoBackendApplication - Running with Spring Boot v3.2.4, Spring v6.1.5
2025-07-24 15:48:09 [main] INFO  a.g.m.c.ConcursoBackendApplication - The following 1 profile is active: "dev"
2025-07-24 15:48:13 [main] INFO  a.g.m.c.auth.domain.jwt.JwtProvider - Clave JWT inicializada con HMAC-SHA256
2025-07-24 15:48:13 [main] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Filter 'jwtTokenFilter' configured for use
2025-07-24 15:48:13 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-07-24 15:48:14 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-24 15:48:17 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Using document storage location: './document-storage-dev'
2025-07-24 15:48:17 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Created document storage directory: ./document-storage-dev
2025-07-24 15:48:19 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 15:48:20 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 124 mappings in 'requestMappingHandlerMapping'
2025-07-24 15:48:20 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-24 15:48:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2d3e28ba, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f182a43, org.springframework.security.web.context.SecurityContextHolderFilter@5938d600, org.springframework.security.web.header.HeaderWriterFilter@581c0da6, org.springframework.web.filter.CorsFilter@69b039f6, org.springframework.security.web.authentication.logout.LogoutFilter@62b81449, ar.gov.mpd.concursobackend.auth.domain.jwt.JwtTokenFilter@1686ed85, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55aa7cc0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5a6e1f51, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@14719b71, org.springframework.security.web.session.SessionManagementFilter@779be9bb, org.springframework.security.web.access.ExceptionTranslationFilter@432c9f47, org.springframework.security.web.access.intercept.AuthorizationFilter@7cd006ea]
2025-07-24 15:48:20 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-24 15:48:20 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 3 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-24 15:48:21 [main] INFO  a.g.m.c.ConcursoBackendApplication - Started ConcursoBackendApplication in 12.469 seconds (process running for 13.374)
2025-07-24 15:48:21 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Iniciando limpieza programada de entradas antiguas en la cola de documentos
2025-07-24 15:48:21 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Limpieza de cola de documentos completada
2025-07-24 15:48:21 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Iniciando proceso de congelación de inscripciones con plazo vencido
2025-07-24 15:48:21 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificando roles básicos del sistema...
2025-07-24 15:48:21 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ie1_0.id,
        ie1_0.accepted_terms,
        ie1_0.centro_de_vida,
        ie1_0.confirmed_personal_data,
        ie1_0.contest_id,
        ie1_0.created_at,
        ie1_0.current_step,
        ie1_0.data_confirmation_date,
        ie1_0.documentation_deadline,
        ie1_0.frozen_date,
        ie1_0.inscription_date,
        ie1_0.status,
        ie1_0.terms_acceptance_date,
        ie1_0.updated_at,
        ie1_0.user_id 
    from
        inscriptions ie1_0 
    where
        ie1_0.status=? 
        and ie1_0.documentation_deadline<?
2025-07-24 15:48:21 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:48:21 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Encontradas 0 inscripciones con plazo de documentación vencido
2025-07-24 15:48:21 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Proceso de congelación completado. 0 inscripciones procesadas
2025-07-24 15:48:21 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_USER ya existe, no es necesario crearlo
2025-07-24 15:48:21 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 15:48:21 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_ADMIN ya existe, no es necesario crearlo
2025-07-24 15:48:21 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificación de roles básicos completada.
2025-07-24 15:48:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6a38a87c
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@f498ae5
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-24 15:48:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:23 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:23 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:28 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:28 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:33 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:33 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:38 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:43 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:43 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /api/auth/login
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /api/auth/login: true
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /api/auth/login
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /api/auth/login: true
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to ar.gov.mpd.concursobackend.auth.infrastructure.controller.AuthController#login(UserLogin, BindingResult)
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [UserLogin(username=admin, password=admin123)]
2025-07-24 15:48:44 [http-nio-8080-exec-9] INFO  a.g.m.c.a.i.c.AuthController - Intento de login para usuario: admin
2025-07-24 15:48:44 [http-nio-8080-exec-9] INFO  a.g.m.c.a.a.service.UserService - Intentando autenticar al usuario: admin
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        ue1_0.id 
    from
        user_entity ue1_0 
    where
        ue1_0.username=? 
    limit
        ?
2025-07-24 15:48:44 [http-nio-8080-exec-9] ERROR a.g.m.c.a.a.service.UserService - Usuario no encontrado: admin
2025-07-24 15:48:44 [http-nio-8080-exec-9] ERROR a.g.m.c.a.i.c.AuthController - Credenciales inválidas para usuario: admin
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ar.gov.mpd.concursobackend.auth.infrastructure.controller.AuthController$ErrorResponse@4bdd079d]
2025-07-24 15:48:44 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 401 UNAUTHORIZED
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:48 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:48 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:53 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:53 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:48:58 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:48:58 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:49:03 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:49:03 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:49:08 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:49:08 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:49:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:49:38 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:50:08 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:50:08 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:50:38 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:50:38 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:51:08 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:51:08 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:51:38 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:51:38 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:52:08 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:52:08 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:52:38 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:52:38 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:53:09 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:53:09 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:53:39 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:53:39 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:54:09 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:54:09 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:54:39 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:54:39 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:55:09 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:55:09 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:55:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:55:39 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:56:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:56:09 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:56:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:56:39 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:57:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:57:09 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:57:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:57:39 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:58:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:58:09 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:58:39 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:58:39 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:59:09 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:59:09 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 15:59:39 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 15:59:39 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:00:09 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:00:09 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:00:39 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:00:39 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:01:09 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:01:09 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:01:39 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:01:39 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:02:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:02:09 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:02:39 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:02:39 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:03:09 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:03:09 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:03:39 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:03:39 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:04:09 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:04:09 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:04:39 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:04:39 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:05:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:05:10 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:05:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:05:40 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:06:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:06:10 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:06:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:06:40 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:07:10 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:07:10 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:07:40 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:07:40 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:08:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:08:10 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:08:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:08:40 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:09:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:09:10 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:09:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:09:40 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:10:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:10:10 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:10:40 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:10:40 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:11:10 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:11:10 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:11:40 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:11:40 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:12:10 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:12:10 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:12:40 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:12:40 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:13:10 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:13:10 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:13:40 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:13:40 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:14:10 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:14:10 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:14:40 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:14:40 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:15:10 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:15:10 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:15:40 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:15:40 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:16:10 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:16:10 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:16:40 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:16:40 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:17:10 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:17:10 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:17:40 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:17:40 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:18:10 [http-nio-8080-exec-6] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:18:10 [http-nio-8080-exec-6] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:18:41 [http-nio-8080-exec-8] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:18:41 [http-nio-8080-exec-8] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:19:11 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:19:11 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:19:41 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:19:41 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:20:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:20:11 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:20:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:20:41 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:21:11 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:21:11 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:21:41 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:21:41 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:22:11 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:22:11 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:22:41 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:22:41 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:23:11 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:23:11 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:23:41 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:23:41 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:24:11 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:24:11 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:24:41 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:24:41 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:25:11 [http-nio-8080-exec-3] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:25:11 [http-nio-8080-exec-3] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 16:25:41 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 16:25:41 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:24:41 [main] INFO  a.g.m.c.ConcursoBackendApplication - Starting ConcursoBackendApplication v0.0.1 using Java 21.0.7 with PID 1 (/app/app.jar started by root in /app)
2025-07-24 21:24:41 [main] DEBUG a.g.m.c.ConcursoBackendApplication - Running with Spring Boot v3.2.4, Spring v6.1.5
2025-07-24 21:24:41 [main] INFO  a.g.m.c.ConcursoBackendApplication - The following 1 profile is active: "dev"
2025-07-24 21:24:45 [main] INFO  a.g.m.c.auth.domain.jwt.JwtProvider - Clave JWT inicializada con HMAC-SHA256
2025-07-24 21:24:45 [main] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Filter 'jwtTokenFilter' configured for use
2025-07-24 21:24:45 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-07-24 21:24:46 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-24 21:24:48 [main] INFO  a.g.m.c.d.i.s.FileSystemDocumentStorageService - Using document storage location: './document-storage-dev'
2025-07-24 21:24:50 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 21:24:50 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 124 mappings in 'requestMappingHandlerMapping'
2025-07-24 21:24:51 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-24 21:24:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@74918273, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c6f7b6b, org.springframework.security.web.context.SecurityContextHolderFilter@3486cb21, org.springframework.security.web.header.HeaderWriterFilter@24b83648, org.springframework.web.filter.CorsFilter@5938d600, org.springframework.security.web.authentication.logout.LogoutFilter@432c9f47, ar.gov.mpd.concursobackend.auth.domain.jwt.JwtTokenFilter@e91facf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@70d52d7e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5a31d662, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15d42b6f, org.springframework.security.web.session.SessionManagementFilter@5743cf1c, org.springframework.security.web.access.ExceptionTranslationFilter@5bcaebdb, org.springframework.security.web.access.intercept.AuthorizationFilter@b9645ca]
2025-07-24 21:24:51 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-24 21:24:51 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 3 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-24 21:24:52 [main] INFO  a.g.m.c.ConcursoBackendApplication - Started ConcursoBackendApplication in 11.991 seconds (process running for 13.503)
2025-07-24 21:24:52 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Iniciando limpieza programada de entradas antiguas en la cola de documentos
2025-07-24 21:24:52 [scheduling-1] DEBUG a.g.m.c.d.i.s.DocumentQueueCleanupScheduler - Limpieza de cola de documentos completada
2025-07-24 21:24:52 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Iniciando proceso de congelación de inscripciones con plazo vencido
2025-07-24 21:24:52 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ie1_0.id,
        ie1_0.accepted_terms,
        ie1_0.centro_de_vida,
        ie1_0.confirmed_personal_data,
        ie1_0.contest_id,
        ie1_0.created_at,
        ie1_0.current_step,
        ie1_0.data_confirmation_date,
        ie1_0.documentation_deadline,
        ie1_0.frozen_date,
        ie1_0.inscription_date,
        ie1_0.status,
        ie1_0.terms_acceptance_date,
        ie1_0.updated_at,
        ie1_0.user_id 
    from
        inscriptions ie1_0 
    where
        ie1_0.status=? 
        and ie1_0.documentation_deadline<?
2025-07-24 21:24:52 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificando roles básicos del sistema...
2025-07-24 21:24:52 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Encontradas 0 inscripciones con plazo de documentación vencido
2025-07-24 21:24:52 [scheduling-1] INFO  a.g.m.c.i.a.s.InscriptionDeadlineService - Proceso de congelación completado. 0 inscripciones procesadas
2025-07-24 21:24:52 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 21:24:52 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_USER ya existe, no es necesario crearlo
2025-07-24 21:24:52 [main] DEBUG org.hibernate.SQL - 
    select
        re1_0.id 
    from
        roles re1_0 
    where
        re1_0.name=? 
    limit
        ?
2025-07-24 21:24:52 [main] DEBUG a.g.m.c.a.i.config.RoleInitializer - El rol ROLE_ADMIN ya existe, no es necesario crearlo
2025-07-24 21:24:52 [main] INFO  a.g.m.c.a.i.config.RoleInitializer - Verificación de roles básicos completada.
2025-07-24 21:24:53 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@763a53c1
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@24a170e0
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-24 21:24:53 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:24:53 [http-nio-8080-exec-1] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:24:53 [http-nio-8080-exec-1] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:24:58 [http-nio-8080-exec-2] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:24:58 [http-nio-8080-exec-2] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:25:03 [http-nio-8080-exec-4] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:25:03 [http-nio-8080-exec-4] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:25:09 [http-nio-8080-exec-5] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:25:09 [http-nio-8080-exec-5] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:25:14 [http-nio-8080-exec-7] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:25:14 [http-nio-8080-exec-7] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:25:19 [http-nio-8080-exec-9] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:25:19 [http-nio-8080-exec-9] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Checking if should filter path: /actuator/health
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Should not filter path /actuator/health: false
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Authorization header: null
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - No se encontró el header Authorization o no tiene el formato correcto
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Headers de la petición:
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - host: localhost:8080
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - user-agent: curl/7.81.0
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - accept: */*
2025-07-24 21:25:24 [http-nio-8080-exec-10] DEBUG a.g.m.c.a.domain.jwt.JwtTokenFilter - Processing request for path: /actuator/health with token: absent
2025-07-24 21:25:24 [http-nio-8080-exec-10] WARN  a.g.m.c.a.domain.jwt.JwtTokenFilter - No se proporcionó token para la ruta: /actuator/health
