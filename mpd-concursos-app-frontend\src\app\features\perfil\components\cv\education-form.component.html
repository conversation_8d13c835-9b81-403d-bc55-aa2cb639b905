<!-- Formulario Inteligente de Educación -->
<div class="education-form">

  <!-- Header del formulario (solo si no está en modal) -->
  <div class="form-header" *ngIf="!isInModal">
    <h3 class="form-title">
      <i class="fas fa-graduation-cap"></i>
      {{ mode === 'create' ? 'Agregar Educación' : 'Editar Educación' }}
    </h3>
    <p class="form-subtitle">
      Completa la información sobre tu formación académica
    </p>
  </div>

  <!-- Alertas de validación -->
  <div class="validation-alerts" *ngIf="hasErrors() || hasWarnings()">
    <div class="alert alert-error" *ngIf="hasErrors()">
      <i class="fas fa-exclamation-circle"></i>
      <div class="alert-content">
        <strong>Errores de validación:</strong>
        <ul>
          <li *ngFor="let error of validationState().errors">{{ error }}</li>
        </ul>
      </div>
    </div>

    <div class="alert alert-warning" *ngIf="hasWarnings()">
      <i class="material-icons">warning</i>
      <div class="alert-content">
        <strong>Advertencias:</strong>
        <ul>
          <li *ngFor="let warning of validationState().warnings">{{ warning }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- Formulario -->
  <form
    id="education-form"
    [formGroup]="form()!"
    *ngIf="form()"
    (ngSubmit)="onSave()"
    class="dynamic-form"
    novalidate>

    <!-- Campos dinámicos -->
    <div class="form-grid">
      <ng-container *ngFor="let field of visibleFields(); trackBy: trackByFieldName">
        <div
          class="form-field"
          [class.full-width]="field.type === 'textarea' || field.type === 'chips'">

          <!-- Campo de texto -->
          <app-custom-form-field
            *ngIf="field.type === 'text'"
            [label]="field.label"
            [placeholder]="field.placeholder || ''"
            [required]="field.required"
            [formControlName]="field.name"
            [hint]="field.helpText || ''">
          </app-custom-form-field>

          <!-- Campo de área de texto -->
          <app-custom-form-field
            *ngIf="field.type === 'textarea'"
            [label]="field.label"
            [required]="field.required"
            [placeholder]="field.placeholder || ''"
            [formControlName]="field.name"
            [hint]="field.helpText || ''"
            type="textarea"
            rows="4">
          </app-custom-form-field>

          <!-- Campo de fecha -->
          <app-custom-datepicker
            *ngIf="field.type === 'date'"
            [label]="field.label"
            [required]="field.required"
            [formControlName]="field.name"
            [hint]="field.helpText || ''">
          </app-custom-datepicker>

          <!-- Campo de select -->
          <app-custom-select
            *ngIf="field.type === 'select'"
            [label]="field.label"
            [options]="getSelectOptions(field.name)"
            [formControlName]="field.name"
            [required]="field.required"
            [placeholder]="field.placeholder || 'Selecciona una opción'"
            [hint]="field.helpText || ''">
          </app-custom-select>

          <!-- Campo de número -->
          <app-custom-form-field
            *ngIf="field.type === 'number'"
            [label]="field.label"
            [type]="'number'"
            [placeholder]="field.placeholder || ''"
            [required]="field.required"
            [formControlName]="field.name"
            [hint]="field.helpText || ''"
            [min]="field.min || null"
            [max]="field.max || null">
          </app-custom-form-field>

          <!-- Campo de checkbox -->
          <div class="checkbox-field" *ngIf="field.type === 'checkbox'">
            <label class="checkbox-label">
              <input
                type="checkbox"
                [formControlName]="field.name"
                [attr.disabled]="isLoading ? '' : null"
                class="checkbox-input">
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">{{ field.label }}</span>
            </label>
            <p class="field-help" *ngIf="field.helpText">{{ field.helpText }}</p>
          </div>

          <!-- Campo de chips (habilidades) -->
          <div class="chips-field" *ngIf="field.type === 'chips'">
            <label class="field-label">
              {{ field.label }}
              <span class="required-indicator" *ngIf="field.required">*</span>
            </label>

            <!-- Input para agregar chips -->
            <div class="chips-input-container">
              <input
                type="text"
                class="chips-input"
                [placeholder]="field.placeholder || 'Escribe y presiona Enter'"
                [attr.disabled]="isLoading ? '' : null"
                (keydown.enter)="onChipInputEnter($any($event), field.name, chipInput)"
                #chipInput>
              <button
                type="button"
                class="add-chip-btn"
                [attr.disabled]="isLoading ? '' : null"
                (click)="onAddChip(field.name, chipInput.value); chipInput.value = ''">
                <i class="material-icons">add</i>
              </button>
            </div>

            <!-- Lista de chips -->
            <div class="chips-list" *ngIf="form() && form()!.get(field.name)?.value?.length > 0">
              <div
                class="chip"
                *ngFor="let item of form()!.get(field.name)?.value; let i = index">
                <span class="chip-text">{{ item }}</span>
                <button
                  type="button"
                  class="chip-remove"
                  [attr.disabled]="isLoading ? '' : null"
                  (click)="onRemoveChip(field.name, i)"
                  [title]="'Eliminar'">
                  <i class="material-icons">close</i>
                </button>
              </div>
            </div>

            <!-- Contador de elementos -->
            <div class="chips-counter" *ngIf="form() && form()!.get(field.name)?.value?.length > 0">
              {{ form()!.get(field.name)?.value?.length }} / 15
            </div>

            <p class="field-help" *ngIf="field.helpText">{{ field.helpText }}</p>

            <!-- Errores del campo -->
            <div class="field-errors" *ngIf="getFieldErrors(field.name).length > 0">
              <p class="error-message" *ngFor="let error of getFieldErrors(field.name)">
                <i class="material-icons">error</i>
                {{ error }}
              </p>
            </div>
          </div>
        </div>
      </ng-container>
    </div>

    <!-- Sección de Documentación Requerida -->
    <div class="documentation-section">

      <div class="document-upload-info">
        <div class="info-box">
          <i class="fas fa-info-circle"></i>
          <div class="info-content">
            <p><strong>Documentación obligatoria:</strong> Debes subir un documento que respalde tu educación</p>
            <p><strong>Formato requerido:</strong> Solo archivos PDF</p>
            <p><strong>Cantidad máxima:</strong> 1 archivo por educación</p>
            <p><strong>Importante:</strong> Si tienes múltiples documentos, combínalos en un solo PDF</p>
            <p><strong>Nota:</strong> El botón "Guardar" se habilitará después de subir el documento</p>
          </div>
        </div>
      </div>

      <app-cv-document-uploader
        [documentType]="'education'"
        [entityId]="education?.id || null"
        [required]="true"
        [maxFiles]="1"
        [acceptedFormats]="['pdf']"
        [maxFileSize]="10"
        (documentsChange)="onDocumentsChange($event)"
        (validationChange)="onDocumentValidationChange($event)">
      </app-cv-document-uploader>
    </div>
  </form>

  <!-- Indicador de estado del formulario mejorado -->
  <div class="form-status" *ngIf="isDirty()">
    <div class="status-indicator enhanced" [class.valid]="isFormValid()" [class.invalid]="!isFormValid()">
      <div class="status-icon">
        <i class="material-icons">{{ isFormValid() ? 'check_circle' : 'warning' }}</i>
      </div>
      <div class="status-content">
        <span class="status-text">{{ isFormValid() ? 'Formulario válido' : 'Formulario incompleto' }}</span>
        <span class="status-description" *ngIf="!isFormValid()">
          Completa todos los campos requeridos para continuar
        </span>
        <span class="status-description" *ngIf="isFormValid()">
          Todos los campos están correctamente completados
        </span>
      </div>
    </div>
  </div>

  <!-- Acciones del formulario (solo si no está en modal) -->
  <div class="form-actions" *ngIf="!isInModal">
    <div class="actions-left">
      <app-custom-button
        [label]="'Resetear'"
        [variant]="'stroked'"
        [icon]="'refresh'"
        [disabled]="isLoading || !isDirty()"
        (buttonClick)="onReset()">
      </app-custom-button>
    </div>

    <div class="actions-right">
      <app-custom-button
        [label]="'Cancelar'"
        [variant]="'stroked'"
        [disabled]="isLoading"
        (buttonClick)="onCancel()">
      </app-custom-button>

      <app-custom-button
        [label]="mode === 'create' ? 'Guardar Educación' : 'Actualizar Educación'"
        [variant]="'primary'"
        [icon]="mode === 'create' ? 'save' : 'update'"
        [loading]="isLoading"
        [disabled]="!canSave()"
        (buttonClick)="onSave()">
      </app-custom-button>
    </div>
  </div>

  <!-- Información de ayuda (solo si no está en modal) -->
  <div class="form-help" *ngIf="!isInModal">
    <div class="help-section">
      <h4 class="help-title">
        <i class="material-icons">help</i>
        Consejos para completar tu educación
      </h4>
      <ul class="help-list">
        <li>Incluye toda tu formación académica relevante</li>
        <li>Menciona certificaciones y cursos importantes</li>
        <li>Agrega calificaciones destacadas si las tienes</li>
        <li>Describe proyectos o materias relevantes para tu carrera</li>
        <li>Mantén la información actualizada</li>
      </ul>
    </div>

    <div class="help-section">
      <h4 class="help-title">
        <i class="material-icons">security</i>
        Seguridad y privacidad
      </h4>
      <p class="help-text">
        Toda la información ingresada es sanitizada automáticamente para prevenir vulnerabilidades de seguridad.
        Tus datos están protegidos y solo serán visibles en tu perfil profesional.
      </p>
    </div>
  </div>
</div>
