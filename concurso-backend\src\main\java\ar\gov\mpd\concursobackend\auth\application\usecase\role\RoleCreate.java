package ar.gov.mpd.concursobackend.auth.application.usecase.role;

import ar.gov.mpd.concursobackend.auth.domain.model.Rol;
import ar.gov.mpd.concursobackend.auth.domain.port.IRoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RoleCreate {

    private final IRoleRepository roleRepository;

    public RoleCreate(@Autowired IRoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    public void run(Rol rol) {
        this.roleRepository.create(rol);
    }
}
