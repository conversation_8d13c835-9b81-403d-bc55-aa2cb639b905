<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_lIgXoJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_lIgXoZnXEe-0mq43z-9ehA" bindingContexts="_lIgXqpnXEe-0mq43z-9ehA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_lIgXoZnXEe-0mq43z-9ehA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_ltPcYJnXEe-0mq43z-9ehA" label="%trimmedwindow.label.eclipseSDK" x="26" y="26" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1730633673226"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_ltPcYJnXEe-0mq43z-9ehA" selectedElement="_ltPcYZnXEe-0mq43z-9ehA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_ltPcYZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_l-9egJnXEe-0mq43z-9ehA">
        <children xsi:type="advanced:Perspective" xmi:id="_l-9egJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_l-9egZnXEe-0mq43z-9ehA" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$Ctrl+Shift+T</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_l-9egZnXEe-0mq43z-9ehA" selectedElement="_l-9eipnXEe-0mq43z-9ehA" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_l-9egpnXEe-0mq43z-9ehA" toBeRendered="false" containerData="2500">
              <children xsi:type="basic:PartStack" xmi:id="_l-9eg5nXEe-0mq43z-9ehA" elementId="left" toBeRendered="false" containerData="6000">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9ehJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_l-ouYJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9ehZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_l-rKoJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9ehpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_l-rxsJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9eh5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_l-83cJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_l-9eiJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9eiZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_l-7pUJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_l-9eipnXEe-0mq43z-9ehA" containerData="7500" selectedElement="_l-9elpnXEe-0mq43z-9ehA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_l-9ei5nXEe-0mq43z-9ehA" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9ejJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_l-chIJnXEe-0mq43z-9ehA"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_l-9ejZnXEe-0mq43z-9ehA" toBeRendered="false" containerData="2500">
                  <children xsi:type="basic:PartStack" xmi:id="_l-9ejpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" toBeRendered="false" containerData="5000">
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9ej5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" toBeRendered="false" ref="_l-7CQJnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_l-9ekJnXEe-0mq43z-9ehA" elementId="right" toBeRendered="false" containerData="5000">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9ekZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_l-uN8JnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9ekpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_l-uN8ZnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9ek5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_l-ztgJnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9elJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_l-8QYZnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_l-9elZnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_l-83cZnXEe-0mq43z-9ehA" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_l-9elpnXEe-0mq43z-9ehA" elementId="bottom" containerData="2500" selectedElement="_l-9el5nXEe-0mq43z-9ehA">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9el5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProblemView" ref="_l-rxsZnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9emJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavadocView" ref="_l-sYwJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9emZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.SourceView" ref="_l-sYwZnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9empnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_l-s_0JnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9em5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_l-tm4JnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9enJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_l-tm4ZnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9enZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_l-tm4pnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_l-9enpnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_l-8QYJnXEe-0mq43z-9ehA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_ltPcYpnXEe-0mq43z-9ehA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_ltPcY5nXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_ltNAIJnXEe-0mq43z-9ehA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_ltPcZJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_ltO1UJnXEe-0mq43z-9ehA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_ltPcZZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_ltO1UZnXEe-0mq43z-9ehA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_ltNAIJnXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_ltO1UJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_miN8kJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_miN8kZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_ltO1UZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_l-chIJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_l-chIZnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-ouYJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;Aggregate for window 1730633673226&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.mylyn.java.ui.MembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_mHM0gJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_mHNbkJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-rKoJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-rxsJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-rxsZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_mYzh4JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_mYzh4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-sYwJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-sYwZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-s_0JnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-tm4JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-tm4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-tm4pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-uN8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_mW5dYJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_mW5dYZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-uN8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-ztgJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-7CQJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xD;&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xD;&#xA;&lt;sorter>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;filteredTreeFindHistory/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_mP2aMJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_mP2aMZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-7pUJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-8QYJnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-8QYZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-83cJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_l-83cZnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_lIgXopnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_lwN34JnXEe-0mq43z-9ehA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwN34ZnXEe-0mq43z-9ehA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwQ7MJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lwchYJnXEe-0mq43z-9ehA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_lJ1N8ZnXEe-0mq43z-9ehA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwQ7MZnXEe-0mq43z-9ehA" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwQ7MpnXEe-0mq43z-9ehA" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwQ7M5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lwdIcJnXEe-0mq43z-9ehA" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_lJwVTZnXEe-0mq43z-9ehA"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lwdvgJnXEe-0mq43z-9ehA" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_lJwVx5nXEe-0mq43z-9ehA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwRiQJnXEe-0mq43z-9ehA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwRiQZnXEe-0mq43z-9ehA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_mBo_EJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_mAg9sJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_mBSZwJnXEe-0mq43z-9ehA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwRiQpnXEe-0mq43z-9ehA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwRiQ5nXEe-0mq43z-9ehA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwRiRJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lwe9oJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_lJ1NrJnXEe-0mq43z-9ehA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwSJUJnXEe-0mq43z-9ehA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwSJUZnXEe-0mq43z-9ehA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwSJUpnXEe-0mq43z-9ehA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_lwSJU5nXEe-0mq43z-9ehA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_lwSJVJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_lzZHsJnXEe-0mq43z-9ehA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_lza84JnXEe-0mq43z-9ehA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_lIgXo5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_lIgXpJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_lIgXpZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_lIgXppnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_lIgXp5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_mmnUYJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_lIgXqJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_lIgXqZnXEe-0mq43z-9ehA" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_lIgXqpnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgi1ZnXEe-0mq43z-9ehA" keySequence="CTRL+1" command="_lJwVEpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi7pnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+I" command="_lJwU9ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi-ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+L" command="_lJ1OJpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjBpnXEe-0mq43z-9ehA" keySequence="CTRL+SPACE" command="_lJ1Nx5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjDZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+D" command="_lJ1ORpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTwpnXEe-0mq43z-9ehA" keySequence="CTRL+V" command="_lJmkLpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT2pnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+SPACE" command="_lJwVL5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT25nXEe-0mq43z-9ehA" keySequence="CTRL+A" command="_lJwVpZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI9pnXEe-0mq43z-9ehA" keySequence="CTRL+C" command="_lJz_SpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-KJnXEe-0mq43z-9ehA" keySequence="CTRL+X" command="_lJwVU5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-KZnXEe-0mq43z-9ehA" keySequence="CTRL+Y" command="_lJwVx5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-LJnXEe-0mq43z-9ehA" keySequence="CTRL+Z" command="_lJwVTZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMRJnXEe-0mq43z-9ehA" keySequence="ALT+PAGE_UP" command="_lJwV05nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMRZnXEe-0mq43z-9ehA" keySequence="ALT+PAGE_DOWN" command="_lJ1NfZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMSZnXEe-0mq43z-9ehA" keySequence="SHIFT+INSERT" command="_lJmkLpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMTpnXEe-0mq43z-9ehA" keySequence="ALT+F11" command="_lJmkcJnXEe-0mq43z-9ehA">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_lKvMh5nXEe-0mq43z-9ehA" keySequence="CTRL+F10" command="_lJmkT5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMjpnXEe-0mq43z-9ehA" keySequence="CTRL+INSERT" command="_lJz_SpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0EwpnXEe-0mq43z-9ehA" keySequence="CTRL+PAGE_UP" command="_lJ1OCJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0Ew5nXEe-0mq43z-9ehA" keySequence="CTRL+PAGE_DOWN" command="_lJwVGpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0ExZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+F3" command="_lJ1N-5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r0ZnXEe-0mq43z-9ehA" keySequence="SHIFT+DEL" command="_lJwVU5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKXY0JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_lJ6F2JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgiwJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+CR" command="_lJ1N-pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgiwZnXEe-0mq43z-9ehA" keySequence="CTRL+BS" command="_lJmkA5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi0pnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+Q" command="_lJwU-5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi8ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+J" command="_lJwU8JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi9ZnXEe-0mq43z-9ehA" keySequence="CTRL++" command="_lJ1NYJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi_5nXEe-0mq43z-9ehA" keySequence="CTRL+-" command="_lJwViJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjDJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+C" command="_lJmkEpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjFJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F" command="_lJwVOpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjHZnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+J" command="_lJwVCpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjJZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+A" command="_lJz_Z5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjMZnXEe-0mq43z-9ehA" keySequence="CTRL+T" command="_lJ1OWZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTx5nXEe-0mq43z-9ehA" keySequence="CTRL+J" command="_lJmkVpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTy5nXEe-0mq43z-9ehA" keySequence="CTRL+L" command="_lJ1N2pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT05nXEe-0mq43z-9ehA" keySequence="CTRL+O" command="_lJz_T5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT2JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+/" command="_lJwVQpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI_JnXEe-0mq43z-9ehA" keySequence="CTRL+D" command="_lJmkYZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-IpnXEe-0mq43z-9ehA" keySequence="CTRL+=" command="_lJ1NYJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-JpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Y" command="_lJmj-JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-L5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+DEL" command="_lJ1NzJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-MJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+X" command="_lJz_UZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-MZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+Y" command="_lJwVhZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-NZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+\" command="_lJz_e5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-NpnXEe-0mq43z-9ehA" keySequence="CTRL+DEL" command="_lJwVR5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-OZnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_UP" command="_lJ1Of5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMQJnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_DOWN" command="_lJ1NiZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMRpnXEe-0mq43z-9ehA" keySequence="SHIFT+END" command="_lJwVkZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMT5nXEe-0mq43z-9ehA" keySequence="SHIFT+HOME" command="_lJwVdZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMVZnXEe-0mq43z-9ehA" keySequence="END" command="_lJ1OFZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMV5nXEe-0mq43z-9ehA" keySequence="INSERT" command="_lJz_hZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMXZnXEe-0mq43z-9ehA" keySequence="F2" command="_lJwVHJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMaZnXEe-0mq43z-9ehA" keySequence="HOME" command="_lJ1OMJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMbJnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+ARROW_UP" command="_lJ1OW5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMbpnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+ARROW_DOWN" command="_lJwVrZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMc5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+INSERT" command="_lJwU3pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMeJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_lJwVlZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMepnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_lJwU5ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMiJnXEe-0mq43z-9ehA" keySequence="CTRL+F10" command="_lJ1N9ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMjJnXEe-0mq43z-9ehA" keySequence="CTRL+END" command="_lJ1Ni5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMmJnXEe-0mq43z-9ehA" keySequence="CTRL+ARROW_UP" command="_lJmkgZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMmZnXEe-0mq43z-9ehA" keySequence="CTRL+ARROW_DOWN" command="_lJ6F0pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0EwJnXEe-0mq43z-9ehA" keySequence="CTRL+ARROW_LEFT" command="_lJz_RJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0EwZnXEe-0mq43z-9ehA" keySequence="CTRL+ARROW_RIGHT" command="_lJwU-ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0ExJnXEe-0mq43z-9ehA" keySequence="CTRL+HOME" command="_lJmkLJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0ExpnXEe-0mq43z-9ehA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_lJ1NmZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0Ex5nXEe-0mq43z-9ehA" keySequence="CTRL+NUMPAD_ADD" command="_lJ1OS5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0EyJnXEe-0mq43z-9ehA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_lJ1N-JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r0JnXEe-0mq43z-9ehA" keySequence="CTRL+NUMPAD_DIVIDE" command="_lJmkhJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r2JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_lJ1NoZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S4pnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_lJz_h5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hAJnXEe-0mq43z-9ehA" keySequence="ALT+/" command="_lJ1ONJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hBJnXEe-0mq43z-9ehA" keySequence="SHIFT+CR" command="_lJ1OL5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgiwpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_lJ6F6ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgiw5nXEe-0mq43z-9ehA" keySequence="CTRL+CR" command="_lJwVPZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjLJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+C" command="_lJz_MpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT4JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJwVk5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJAJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+U" command="_lJ1NlJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJC5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+I" command="_lJwViZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-N5nXEe-0mq43z-9ehA" keySequence="ALT+ARROW_UP" command="_lJ1NbZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-OpnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_DOWN" command="_lJwVQZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMR5nXEe-0mq43z-9ehA" keySequence="SHIFT+INSERT" command="_lJmkdpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMVpnXEe-0mq43z-9ehA" keySequence="INSERT" command="_lJwVgpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMY5nXEe-0mq43z-9ehA" keySequence="F4" command="_lJmkS5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMgJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_UP" command="_lJ1NvJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMhJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_lJwVjpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgixJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.window" bindingContext="_lIgXq5nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgixZnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+T" command="_lJmkTpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgixpnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+L" command="_lJz_epnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgizpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q O" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgiz5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_lKgi0JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q P" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_lKgi1pnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+B" command="_lJ1Nd5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi15nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+R" command="_lJ6F1JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi2JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q Q" command="_lJ1Nb5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi2ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+S" command="_lJ1NVZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi2pnXEe-0mq43z-9ehA" keySequence="CTRL+3" command="_lJwVG5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi25nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+T" command="_lJwVUpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi3ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+T" command="_lJwU0JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi3pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q S" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi35nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_lKgi4JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+U" command="_lJwU3ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi4ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q T" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi4pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_lKgi45nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+V" command="_lJ1OGJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi5pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q V" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi55nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_lKgi65nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+G" command="_lJ1NZ5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi7JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+W" command="_lJwVUZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi7ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+H" command="_lJz_PpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi75nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q H" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_lKgi8pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q J" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi85nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_lKgi9JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+K" command="_lJmkfpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi9pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q K" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi95nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_lKgi-JnXEe-0mq43z-9ehA" keySequence="CTRL+," command="_lJmkMpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi_ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q L" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgi_pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_lKgjAJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+N" command="_lJz_W5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjAZnXEe-0mq43z-9ehA" keySequence="CTRL+." command="_lJ1OXpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjBJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+O" command="_lJ1OQ5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjBZnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+P" command="_lJmkc5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjB5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+B" command="_lJmkf5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjCJnXEe-0mq43z-9ehA" keySequence="CTRL+#" command="_lJmkUJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjD5nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+T" command="_lJz_RZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjEJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+E" command="_lJwU2JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjFZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+G" command="_lJ1Oa5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjF5nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+H" command="_lJmkRpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjGZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q X" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgjGpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_lKgjG5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q Y" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgjHJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_lKgjHpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q Z" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKgjH5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_lKgjI5nXEe-0mq43z-9ehA" keySequence="CTRL+P" command="_lJ1N8ZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjJJnXEe-0mq43z-9ehA" keySequence="CTRL+Q" command="_lJ1OAZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjK5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+C" command="_lJ1Ny5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjLpnXEe-0mq43z-9ehA" keySequence="CTRL+S" command="_lJwVipnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTwJnXEe-0mq43z-9ehA" keySequence="CTRL+U" command="_lJwVw5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTwZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+F" command="_lJ1OBZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTxJnXEe-0mq43z-9ehA" keySequence="CTRL+W" command="_lJwVzpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTxZnXEe-0mq43z-9ehA" keySequence="CTRL+H" command="_lJ1NxpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTyJnXEe-0mq43z-9ehA" keySequence="CTRL+K" command="_lJ1Ne5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTzJnXEe-0mq43z-9ehA" keySequence="CTRL+M" command="_lJ1Nw5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTz5nXEe-0mq43z-9ehA" keySequence="CTRL+N" command="_lJ1ObpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT2ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+P" command="_lJ1NaZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT3ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJwVypnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT4pnXEe-0mq43z-9ehA" keySequence="CTRL+B" command="_lJmkNpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI8JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q B" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKsI8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_lKsI95nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+S" command="_lJz_NJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI-ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+T" command="_lJz_aZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI-pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q C" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKsI-5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_lKsI_ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Q D" command="_lJ1Nb5nXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKsI_pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_lKsJApnXEe-0mq43z-9ehA" keySequence="CTRL+E" command="_lJwVRZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJA5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+V" command="_lJwVlJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJBJnXEe-0mq43z-9ehA" keySequence="CTRL+F" command="_lJmka5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJB5nXEe-0mq43z-9ehA" keySequence="CTRL+G" command="_lJmkBJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJCJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+W" command="_lJ1OWpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJCZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+H" command="_lJwVP5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJCpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+I" command="_lJmkUZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJDZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+J" command="_lJwVQ5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJDpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+K" command="_lJwVjZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-IJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+L" command="_lJwVDZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-IZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+M" command="_lJ1OTJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-I5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+N" command="_lJwVTpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-J5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+Z" command="_lJz_OZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-LZnXEe-0mq43z-9ehA" keySequence="CTRL+_" command="_lJwVMpnXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKt-LpnXEe-0mq43z-9ehA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_lKt-MpnXEe-0mq43z-9ehA" keySequence="CTRL+{" command="_lJwVMpnXEe-0mq43z-9ehA">
      <parameters xmi:id="_lKt-M5nXEe-0mq43z-9ehA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_lKvMQZnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_LEFT" command="_lJmkU5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMQ5nXEe-0mq43z-9ehA" keySequence="ALT+ARROW_RIGHT" command="_lJwVZZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMS5nXEe-0mq43z-9ehA" keySequence="SHIFT+F2" command="_lJz_l5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMTJnXEe-0mq43z-9ehA" keySequence="SHIFT+F5" command="_lJwVspnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMTZnXEe-0mq43z-9ehA" keySequence="ALT+F7" command="_lJz_b5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMUJnXEe-0mq43z-9ehA" keySequence="ALT+F5" command="_lJwVoJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMU5nXEe-0mq43z-9ehA" keySequence="F11" command="_lJ1OP5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMVJnXEe-0mq43z-9ehA" keySequence="F12" command="_lJ1NyZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMXJnXEe-0mq43z-9ehA" keySequence="F2" command="_lJmkNZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMX5nXEe-0mq43z-9ehA" keySequence="F3" command="_lJwVC5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMYZnXEe-0mq43z-9ehA" keySequence="F4" command="_lJmkP5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMZ5nXEe-0mq43z-9ehA" keySequence="F5" command="_lJwVbZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMapnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F7" command="_lJ1OQZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMa5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F8" command="_lJwVMZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMbZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F9" command="_lJwVnZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMb5nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+ARROW_LEFT" command="_lJ1OAZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMcJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F11" command="_lJz_N5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMcZnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+ARROW_RIGHT" command="_lJmkhpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMcpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F12" command="_lJmkGJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMdJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F4" command="_lJwVUZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMdZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F6" command="_lJ1NXZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMeZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+X J" command="_lJ1NgpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMe5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+X M" command="_lJwVqpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMfJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+X A" command="_lJmkMJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMfZnXEe-0mq43z-9ehA" keySequence="CTRL+F7" command="_lJz_S5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMgZnXEe-0mq43z-9ehA" keySequence="CTRL+F8" command="_lJwVFJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMhZnXEe-0mq43z-9ehA" keySequence="CTRL+F9" command="_lJwU3JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMiZnXEe-0mq43z-9ehA" keySequence="CTRL+F11" command="_lJ1OFpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMi5nXEe-0mq43z-9ehA" keySequence="CTRL+F12" command="_lJmkgJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMk5nXEe-0mq43z-9ehA" keySequence="CTRL+F4" command="_lJwVzpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMlZnXEe-0mq43z-9ehA" keySequence="CTRL+F6" command="_lJmkdJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMlpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+F7" command="_lJ1NjZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMl5nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+X G" command="_lJ1OO5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r0pnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_lJz_lJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r05nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_lJ1Oe5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r1JnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_lJz_gZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r15nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_lJ1NVJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK0r2ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+X Q" command="_lJwU85nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S4JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+X T" command="_lJwVo5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S4ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_lJwVNJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S45nXEe-0mq43z-9ehA" keySequence="ALT+CTRL+SHIFT+F12" command="_lJ1OTZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S5JnXEe-0mq43z-9ehA" keySequence="DEL" command="_lJmkeZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK15-5nXEe-0mq43z-9ehA" keySequence="ALT+-" command="_lJz_WpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hAZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E E" command="_lJmkVJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hApnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E G" command="_lJwVWZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hA5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E J" command="_lJmkLZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hBZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E S" command="_lJwVppnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hBpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E T" command="_lJmkTJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hB5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E L" command="_lJmkM5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hCJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E N" command="_lJ1OYpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK2hCZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E P" command="_lJmj_pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IEJnXEe-0mq43z-9ehA" keySequence="ALT+CR" command="_lJ1NuJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IEZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+E R" command="_lJwU_pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IEpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+D A" command="_lJ1N3JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IE5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+D T" command="_lJmkDpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IFJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+D J" command="_lJ1NpJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK3IFZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+D Q" command="_lJwVuZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgix5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_lJ6F55nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgiyJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+P" command="_lJ1NjJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi3JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+T" command="_lJwVUpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi6JnXEe-0mq43z-9ehA" keySequence="CTRL+7" command="_lJz_XpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgi-pnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+M" command="_lJwVHZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjApnXEe-0mq43z-9ehA" keySequence="CTRL+/" command="_lJz_XpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjCZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+C" command="_lJz_XpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjE5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F" command="_lJ1OLpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjKJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+B" command="_lJ1Og5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjMJnXEe-0mq43z-9ehA" keySequence="CTRL+T" command="_lJz_ZZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTxpnXEe-0mq43z-9ehA" keySequence="CTRL+I" command="_lJwVY5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT0pnXEe-0mq43z-9ehA" keySequence="CTRL+O" command="_lJwVp5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT15nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+/" command="_lJwVxJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT3pnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJwVypnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI_5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+U" command="_lJ1Nq5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJBZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+'" command="_lJz_dJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-JZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+O" command="_lJwVSJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-NJnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+\" command="_lJmkYpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMdpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+ARROW_UP" command="_lJwV0JnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMd5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_lJwVqJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMfpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_UP" command="_lJwVuJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMgpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_lJmkgpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMhpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_lJwVbJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMipnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_lJmkXJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMkpnXEe-0mq43z-9ehA" keySequence="CTRL+F3" command="_lJ1OXZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S6pnXEe-0mq43z-9ehA" keySequence="CTRL+2 F" command="_lJ1OSpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1595nXEe-0mq43z-9ehA" keySequence="CTRL+2 R" command="_lJ1NupnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK15-JnXEe-0mq43z-9ehA" keySequence="CTRL+2 T" command="_lJz_XZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK15-ZnXEe-0mq43z-9ehA" keySequence="CTRL+2 L" command="_lJmkV5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK15-pnXEe-0mq43z-9ehA" keySequence="CTRL+2 M" command="_lJwVcJnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgiypnXEe-0mq43z-9ehA" elementId="org.eclipse.core.runtime.xml" bindingContext="_lKgiyZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgiy5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+P" command="_lJ1NypnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjDpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+D" command="_lJwVwpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgizJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_lJ6F4ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgizZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+P" command="_lJwVf5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjFpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+G" command="_lJ1OFJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjGJnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+H" command="_lJ1N0pnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT35nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJmkNZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMYJnXEe-0mq43z-9ehA" keySequence="F3" command="_lJ1OApnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMYpnXEe-0mq43z-9ehA" keySequence="F4" command="_lJmkAJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMf5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_UP" command="_lJz_apnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMg5nXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_lJz_bZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgi05nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_lJ6F4pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgi1JnXEe-0mq43z-9ehA" keySequence="CTRL+1" command="_lJ1OaJnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgi5JnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_lJ6F1pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgi5ZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+V" command="_lJwVfZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjC5nXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+C" command="_lJ1NsZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-OJnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_UP" command="_lJmkAZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMQpnXEe-0mq43z-9ehA" keySequence="ALT+ARROW_RIGHT" command="_lJ1OPpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMSJnXEe-0mq43z-9ehA" keySequence="SHIFT+INSERT" command="_lJwVfZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMjZnXEe-0mq43z-9ehA" keySequence="CTRL+INSERT" command="_lJ1NsZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgi6ZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_lJ6F8JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgi6pnXEe-0mq43z-9ehA" keySequence="CTRL+7" command="_lJz_XpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjA5nXEe-0mq43z-9ehA" keySequence="CTRL+/" command="_lJz_XpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjCpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+C" command="_lJz_XpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgi-5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_lJ6F6pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgi_JnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+M" command="_lJmkHZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjLZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+C" command="_lJz_MpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT1JnXEe-0mq43z-9ehA" keySequence="CTRL+O" command="_lJ1OVJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT4ZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJwVk5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsI-JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+S" command="_lJwVWJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJAZnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+U" command="_lJ1NlJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJDJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+I" command="_lJwViZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgjEZnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_lJ6F7pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgjEpnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+F" command="_lJ1OLpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqT3JnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+R" command="_lJmkPpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKt-JJnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+O" command="_lJmkC5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMSpnXEe-0mq43z-9ehA" keySequence="SHIFT+F2" command="_lJz_dpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMXpnXEe-0mq43z-9ehA" keySequence="F3" command="_lJmkEZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgjIJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_lJ6F4JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgjIZnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+M" command="_lJwVopnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjIpnXEe-0mq43z-9ehA" keySequence="ALT+CTRL+N" command="_lJ1OTpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKgjL5nXEe-0mq43z-9ehA" keySequence="CTRL+T" command="_lJwVH5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTw5nXEe-0mq43z-9ehA" keySequence="CTRL+W" command="_lJz_jZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTzpnXEe-0mq43z-9ehA" keySequence="CTRL+N" command="_lJ1NY5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgjJpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_lJ6F45nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgjJ5nXEe-0mq43z-9ehA" keySequence="CTRL+R" command="_lJz_TpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMUZnXEe-0mq43z-9ehA" keySequence="F7" command="_lJ1OY5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMUpnXEe-0mq43z-9ehA" keySequence="F8" command="_lJz_gJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMZpnXEe-0mq43z-9ehA" keySequence="F5" command="_lJmkQ5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMaJnXEe-0mq43z-9ehA" keySequence="F6" command="_lJwVlpnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMkZnXEe-0mq43z-9ehA" keySequence="CTRL+F2" command="_lJ1Nz5nXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMlJnXEe-0mq43z-9ehA" keySequence="CTRL+F5" command="_lJ1OOpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKgjKZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_lJ6F5pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKgjKpnXEe-0mq43z-9ehA" keySequence="ALT+SHIFT+B" command="_lJ1Og5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKqTyZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_lJ6F5JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKqTypnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+," command="_lJ1OBJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKqTzZnXEe-0mq43z-9ehA" keySequence="CTRL+SHIFT+." command="_lJ1NwZnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKsJBpnXEe-0mq43z-9ehA" keySequence="CTRL+G" command="_lJ1NwpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKqT0JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_lJ6F2ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKqT0ZnXEe-0mq43z-9ehA" keySequence="CTRL+O" command="_lJwVt5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKqT1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_lJ6F7JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKqT1pnXEe-0mq43z-9ehA" keySequence="CTRL+O" command="_lJmkJ5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKsI8pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_lJ6F8ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKsI85nXEe-0mq43z-9ehA" keySequence="CTRL+C" command="_lJwVJJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lKvMmpnXEe-0mq43z-9ehA" keySequence="CTRL+ARROW_LEFT" command="_lJmkXpnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKsI9JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_lJ6F5ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKsI9ZnXEe-0mq43z-9ehA" keySequence="CTRL+C" command="_lJmkdZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKt-KpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.console" bindingContext="_lJ6F25nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKt-K5nXEe-0mq43z-9ehA" keySequence="CTRL+Z" command="_lJ1OV5nXEe-0mq43z-9ehA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_lKvMWJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_lJ6F65nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKvMWZnXEe-0mq43z-9ehA" keySequence="F1" command="_lJmkD5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKvMWpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_lJ6F8pnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKvMW5nXEe-0mq43z-9ehA" keySequence="F2" command="_lJmke5nXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKvMZJnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_lJ6F7ZnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKvMZZnXEe-0mq43z-9ehA" keySequence="F5" command="_lJ1OLJnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lKvMj5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_lJ6F35nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lKvMkJnXEe-0mq43z-9ehA" keySequence="CTRL+INSERT" command="_lJ1NUJnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lK0r1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_lJ6F6JnXEe-0mq43z-9ehA">
    <bindings xmi:id="_lK0r1pnXEe-0mq43z-9ehA" keySequence="ALT+Y" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S5ZnXEe-0mq43z-9ehA" keySequence="ALT+A" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S5pnXEe-0mq43z-9ehA" keySequence="ALT+B" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S55nXEe-0mq43z-9ehA" keySequence="ALT+C" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S6JnXEe-0mq43z-9ehA" keySequence="ALT+D" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S6ZnXEe-0mq43z-9ehA" keySequence="ALT+E" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S65nXEe-0mq43z-9ehA" keySequence="ALT+F" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S7JnXEe-0mq43z-9ehA" keySequence="ALT+G" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S7ZnXEe-0mq43z-9ehA" keySequence="ALT+P" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1S7pnXEe-0mq43z-9ehA" keySequence="ALT+R" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK158JnXEe-0mq43z-9ehA" keySequence="ALT+S" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK158ZnXEe-0mq43z-9ehA" keySequence="ALT+T" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK158pnXEe-0mq43z-9ehA" keySequence="ALT+V" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK1585nXEe-0mq43z-9ehA" keySequence="ALT+W" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK159JnXEe-0mq43z-9ehA" keySequence="ALT+H" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK159ZnXEe-0mq43z-9ehA" keySequence="ALT+L" command="_lJz_WJnXEe-0mq43z-9ehA"/>
    <bindings xmi:id="_lK159pnXEe-0mq43z-9ehA" keySequence="ALT+N" command="_lJz_WJnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_lK2hCpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_lJ6F15nXEe-0mq43z-9ehA">
    <bindings xmi:id="_lK2hC5nXEe-0mq43z-9ehA" keySequence="ALT+CR" command="_lJz_PZnXEe-0mq43z-9ehA"/>
  </bindingTables>
  <bindingTables xmi:id="_l-fkcZnXEe-0mq43z-9ehA" bindingContext="_l-fkcJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-gykZnXEe-0mq43z-9ehA" bindingContext="_l-gykJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-gyk5nXEe-0mq43z-9ehA" bindingContext="_l-gykpnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-gylZnXEe-0mq43z-9ehA" bindingContext="_l-gylJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-hZoZnXEe-0mq43z-9ehA" bindingContext="_l-hZoJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-hZo5nXEe-0mq43z-9ehA" bindingContext="_l-hZopnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-hZpZnXEe-0mq43z-9ehA" bindingContext="_l-hZpJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-hZp5nXEe-0mq43z-9ehA" bindingContext="_l-hZppnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-iAsZnXEe-0mq43z-9ehA" bindingContext="_l-iAsJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-iAs5nXEe-0mq43z-9ehA" bindingContext="_l-iAspnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-iAtZnXEe-0mq43z-9ehA" bindingContext="_l-iAtJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-iAt5nXEe-0mq43z-9ehA" bindingContext="_l-iAtpnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-inwJnXEe-0mq43z-9ehA" bindingContext="_l-iAuJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-inwpnXEe-0mq43z-9ehA" bindingContext="_l-inwZnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-inxJnXEe-0mq43z-9ehA" bindingContext="_l-inw5nXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-inxpnXEe-0mq43z-9ehA" bindingContext="_l-inxZnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-inyJnXEe-0mq43z-9ehA" bindingContext="_l-inx5nXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-jO0ZnXEe-0mq43z-9ehA" bindingContext="_l-jO0JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-jO05nXEe-0mq43z-9ehA" bindingContext="_l-jO0pnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-jO1ZnXEe-0mq43z-9ehA" bindingContext="_l-jO1JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-j14ZnXEe-0mq43z-9ehA" bindingContext="_l-j14JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-j145nXEe-0mq43z-9ehA" bindingContext="_l-j14pnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-j15ZnXEe-0mq43z-9ehA" bindingContext="_l-j15JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-j155nXEe-0mq43z-9ehA" bindingContext="_l-j15pnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-kc8ZnXEe-0mq43z-9ehA" bindingContext="_l-kc8JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-kc85nXEe-0mq43z-9ehA" bindingContext="_l-kc8pnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-kc9ZnXEe-0mq43z-9ehA" bindingContext="_l-kc9JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-kc95nXEe-0mq43z-9ehA" bindingContext="_l-kc9pnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-kc-ZnXEe-0mq43z-9ehA" bindingContext="_l-kc-JnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lEAZnXEe-0mq43z-9ehA" bindingContext="_l-lEAJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lEA5nXEe-0mq43z-9ehA" bindingContext="_l-lEApnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lEBZnXEe-0mq43z-9ehA" bindingContext="_l-lEBJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lEB5nXEe-0mq43z-9ehA" bindingContext="_l-lEBpnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lECZnXEe-0mq43z-9ehA" bindingContext="_l-lECJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lrEZnXEe-0mq43z-9ehA" bindingContext="_l-lrEJnXEe-0mq43z-9ehA"/>
  <bindingTables xmi:id="_l-lrE5nXEe-0mq43z-9ehA" bindingContext="_l-lrEpnXEe-0mq43z-9ehA"/>
  <rootContext xmi:id="_lIgXqpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_lIgXq5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_lIgXrJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_lJ6F1pnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_lJ6F15nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_lJ6F2JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_lJ6F2ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_lJ6F4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_lJ6F4pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_lJ6F55nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_lJ6F6pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_lJ6F65nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_lJ6F7JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_lJ6F7pnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_lJ6F8JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_lJ6F25nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_lJ6F3JnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_lJ6F3ZnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_lJ6F35nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_lJ6F4JnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_lJ6F45nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_lJ6F5JnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_lJ6F75nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_lJ6F5ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_lJ6F6JnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_lJ6F6ZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_lJ6F7ZnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_lJ6F8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_lJ6F8pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_lIgXrZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_lJ6F2pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_lJ6F3pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_lJ6F5pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_lKgiyZnXEe-0mq43z-9ehA" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_l-fkcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_l-gykJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_l-gykpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_l-gylJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_l-hZoJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_l-hZopnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_l-hZpJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_l-hZppnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_l-iAsJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_l-iAspnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_l-iAtJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_l-iAtpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_l-iAuJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_l-inwZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_l-inw5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_l-inxZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_l-inx5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_l-jO0JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_l-jO0pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_l-jO1JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_l-j14JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_l-j14pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_l-j15JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_l-j15pnXEe-0mq43z-9ehA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_l-kc8JnXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_l-kc8pnXEe-0mq43z-9ehA" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_l-kc9JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_l-kc9pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_l-kc-JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_l-lEAJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_l-lEApnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_l-lEBJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_l-lEBpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_l-lECJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_l-lrEJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_l-lrEpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_lQIpoJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_lsCigJnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_lsGz8JnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_lsHbAJnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_lsIpIJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsJQMJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsKeUJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsLFYJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsLscJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsM6kJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsNhoJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsOIsJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsPW0JnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsP94JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_lsRMAJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_lsRzEJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_lsTBMJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_lsToQJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_lsU2YJnXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_lsWEgJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsXSoJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsYgwJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_lsZu4JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsa9AJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsbkEJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lscLIJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_lsdZQJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_lseAUJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_lsfOcJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_lsf1gJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lshDoJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_lshqsJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsiRwJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_lsi40JnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_lsjf4JnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_lskuAJnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_lslVEJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_lsmjMJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_lsnKQJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_lsnxUJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lsoYYJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lspmgJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lspmgZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lsq0oJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lssCwJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_lstQ4JnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_lst38JnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_lsufAJnXEe-0mq43z-9ehA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_lsvGEJnXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_lswUMJnXEe-0mq43z-9ehA" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_lsw7QJnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_lsxiUJnXEe-0mq43z-9ehA" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_lsyJYJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_lsywcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_lsz-kJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_ls1MsJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls1zwJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls3B4JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls3o8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls4QAJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls43EJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls6FMJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls8hcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls9IgJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls9vkJnXEe-0mq43z-9ehA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ls_kwJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_lJmj9pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj95nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj-JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj-ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmj-pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_lJmj-5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_lJmj7pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj_JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj_ZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj_pnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmj_5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkAJnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkAZnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_lJmj6pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkApnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkA5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkBJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkBZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkBpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkB5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkCJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkCZnXEe-0mq43z-9ehA" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmkCpnXEe-0mq43z-9ehA" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_lJmkC5nXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkDJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkDZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkDpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkD5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkEJnXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_lJmj8JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkEZnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkEpnXEe-0mq43z-9ehA" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_lJmj0pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkE5nXEe-0mq43z-9ehA" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkFJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkFZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkFpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkF5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkGJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkGZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkGpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkG5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkHJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkHZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_lJmj1ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkHpnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkH5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkIJnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkIZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkIpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmkI5nXEe-0mq43z-9ehA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_lJmkJJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkJZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkJpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkJ5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkKJnXEe-0mq43z-9ehA" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_lJmj35nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkKZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkKpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkK5nXEe-0mq43z-9ehA" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkLJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkLZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkLpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkL5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkMJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkMZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkMpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkM5nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkNJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkNZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkNpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkN5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkOJnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkOZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkOpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkO5nXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkPJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkPZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkPpnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkP5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkQJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkQZnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_lJmj8ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkQpnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkQ5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkRJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkRZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkRpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkR5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkSJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkSZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmkSpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_lJmkS5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkTJnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkTZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkTpnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_lJmj45nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkT5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkUJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkUZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkUpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkU5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkVJnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkVZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkVpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkV5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkWJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkWZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkWpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkW5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkXJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkXZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkXpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkX5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkYJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkYZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkYpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkY5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkZJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkZZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkZpnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkZ5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmkaJnXEe-0mq43z-9ehA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_lJmkaZnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkapnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmka5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkbJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkbZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJmkbpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_lJmkb5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkcZnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkcpnXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_lJmj0JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkc5nXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkdJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkdZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkdpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkd5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkeJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkeZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkepnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmke5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkfJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkfZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkfpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkf5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkgJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkgZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkgpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkg5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_lJmj3pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkhJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkhZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJmkhpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU0JnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU0pnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_lJmj9JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU05nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU1JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_lJmj4pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwU1pnXEe-0mq43z-9ehA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_lJwU15nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU2JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU2ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU2pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU25nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU3JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU3ZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU3pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU35nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU4JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU4pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU45nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU5JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU5ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU5pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_lJmj8JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU55nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU6JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_lJmj15nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwU6ZnXEe-0mq43z-9ehA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_lJwU6pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU65nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU7JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU7ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU7pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU75nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwU8pnXEe-0mq43z-9ehA" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_lJwU85nXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU9JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU9ZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU9pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU95nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU-JnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU-ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU-pnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU-5nXEe-0mq43z-9ehA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU_JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU_ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU_pnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwU_5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVAJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVAZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVApnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVA5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVBJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVBZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVBpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_lJmj6JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVB5nXEe-0mq43z-9ehA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_lJwVCJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVCZnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVCpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVC5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVDJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVDZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVDpnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_lJmj7pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVD5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVEJnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVEZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVEpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVE5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVFJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVFZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVFpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVF5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVGJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_lJmj75nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVGZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_lJwVGpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVG5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVHJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVHZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVHpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_lJmj1ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVH5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVIJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVIZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVIpnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVI5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVJJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVJZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVJpnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVJ5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVKJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_lJmj5pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVKZnXEe-0mq43z-9ehA" elementId="url" name="URL"/>
    <parameters xmi:id="_lJwVKpnXEe-0mq43z-9ehA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_lJwVK5nXEe-0mq43z-9ehA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_lJwVLJnXEe-0mq43z-9ehA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_lJwVLZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVLpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVL5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVMJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVMZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVMpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_lJmj5pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVM5nXEe-0mq43z-9ehA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_lJwVNJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVNZnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVNpnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVN5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVOJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVOZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVOpnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVO5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVPJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVPZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVPpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVP5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVQJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVQZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVQpnXEe-0mq43z-9ehA" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_lJmj0pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVQ5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVRJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVRZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVRpnXEe-0mq43z-9ehA" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_lJmj35nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVR5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVSJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVSZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVSpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVS5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVTJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVTZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVTpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVT5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVUJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVUZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVUpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVU5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVVJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVVZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVVpnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVV5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVWJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_lJmj1ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVWZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVWpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVW5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVXJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVXZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVXpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_lJmj4pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVX5nXEe-0mq43z-9ehA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_lJwVYJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVYZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVYpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_lJmj8JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVY5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVZJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVZZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVZpnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVZ5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVaJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVaZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVapnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVa5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVbJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVbZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVbpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVb5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVcJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVcZnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_lJmj7pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVcpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVc5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVdJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVdZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVdpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVd5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVeJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVeZnXEe-0mq43z-9ehA" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_lJwVepnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVe5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVfJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVfZnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_lJmj6pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVfpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVf5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVgJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVgZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_lJwVgpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVg5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVhJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVhZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVhpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVh5nXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwViJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwViZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVipnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVi5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVjJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVjZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVjpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVj5nXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_lJmj0JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVkJnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVkZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVkpnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVk5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVlJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVlZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVlpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVl5nXEe-0mq43z-9ehA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVmJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_lJmj4pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVmZnXEe-0mq43z-9ehA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_lJwVmpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVm5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVnJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVnZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVnpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVn5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVoJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVoZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVopnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVo5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVpJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVpZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVppnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVp5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVqJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVqZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVqpnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVq5nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVrJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVrZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVrpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVr5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVsJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVsZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVspnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVs5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVtJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVtZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVtpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_lJmj85nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVt5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVuJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVuZnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVupnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVu5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_lJmj5pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVvJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_lJwVvZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_lJmj6JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwVvpnXEe-0mq43z-9ehA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_lJwVv5nXEe-0mq43z-9ehA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_lJwVwJnXEe-0mq43z-9ehA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_lJwVwZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVwpnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVw5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVxJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVxZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVxpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVx5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVyJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVyZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVypnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVy5nXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_lJmj45nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVzJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVzZnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVzpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwVz5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwV0JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwV0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwV0pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_lJwV05nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwV1JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwV1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwV1pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_lJwV15nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJwV2JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_lJmj7ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJwV2ZnXEe-0mq43z-9ehA" elementId="title" name="Title"/>
    <parameters xmi:id="_lJwV2pnXEe-0mq43z-9ehA" elementId="message" name="Message"/>
    <parameters xmi:id="_lJwV25nXEe-0mq43z-9ehA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_lJwV3JnXEe-0mq43z-9ehA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_lJz_MJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_MZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_MpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_M5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_NJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_NZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_NpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_N5nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_OJnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_OZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_OpnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_O5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_PJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_PZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_PpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_P5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_QJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_QZnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_lJmj1pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_QpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_Q5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_RJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_RZnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_lJmj45nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_RpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_lJmj5pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJz_R5nXEe-0mq43z-9ehA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_lJz_SJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_SZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_SpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_S5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_TJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_TZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_TpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_T5nXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_UJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_UZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_UpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_U5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_VJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_VZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_VpnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_V5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_WJnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_lJmj6pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_WZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_WpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_W5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_XJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_XZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_XpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_X5nXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_YJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_YZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_YpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_Y5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_ZJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_ZZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_ZpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_Z5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_aJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_aZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_apnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_a5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_bJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_bZnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_bpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_b5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_cJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_cZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_lJmj75nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJz_cpnXEe-0mq43z-9ehA" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_lJz_c5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_dJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_dZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_dpnXEe-0mq43z-9ehA" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_d5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_eJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_eZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_epnXEe-0mq43z-9ehA" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_lJmj5ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_e5nXEe-0mq43z-9ehA" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_lJmj0pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_fJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_fZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_fpnXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_f5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_gJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_gZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_gpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_g5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_hJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_hZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_hpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_h5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_iJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.bugs.commands.ReportBugAction" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement for predefined Products / Projects" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_iZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_ipnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_i5nXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJz_jJnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_lJz_jZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_jpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_j5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_kJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_kZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_kpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_k5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_lJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_lZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_lpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_l5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_mJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_mZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_mpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_m5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_nJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_nZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_lJmj8JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJz_npnXEe-0mq43z-9ehA" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NUJnXEe-0mq43z-9ehA" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_lJmj35nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NUZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_lJmj05nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NUpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1NU5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NVJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NVZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NVpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NV5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NWJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NWZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NWpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NW5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NXJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NXZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NXpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NX5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NYJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NYZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NYpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NY5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NZJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NZZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NZpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NZ5nXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NaJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NaZnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NapnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Na5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NbJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NbZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NbpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nb5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_lJmj0ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_lJ1NcZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_lJ1NcpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_lJ1Nc5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NdJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NdZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NdpnXEe-0mq43z-9ehA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1Nd5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NeJnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_lJmj4ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NeZnXEe-0mq43z-9ehA" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1NepnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Ne5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NfJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NfZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NfpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nf5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_lJmj3JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NgJnXEe-0mq43z-9ehA" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_lJ1NgZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NgpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Ng5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NhJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_lJmj15nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NhZnXEe-0mq43z-9ehA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_lJ1NhpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_lJmj6JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1Nh5nXEe-0mq43z-9ehA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_lJ1NiJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NiZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NipnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Ni5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NjJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NjZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NjpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nj5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NkJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NkZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NkpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nk5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NlJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NlZnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NlpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nl5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NmJnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_lJmj45nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NmZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NmpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nm5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NnJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NnZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NnpnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nn5nXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NoJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NoZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NopnXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1No5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NpJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NpZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NppnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Np5nXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NqJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NqZnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NqpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nq5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NrJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NrZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NrpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nr5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NsJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NsZnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_lJmj6pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NspnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Ns5nXEe-0mq43z-9ehA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NtJnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_lJmj8ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1NtZnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_lJ1NtpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nt5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NuJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NuZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NupnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nu5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NvJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NvZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NvpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nv5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NwJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NwZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NwpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nw5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NxJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NxZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NxpnXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nx5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NyJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NyZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NypnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Ny5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NzJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NzZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1NzpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Nz5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N0JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N0pnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N05nXEe-0mq43z-9ehA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N1JnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N1pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N15nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_lJmj8JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1N2JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_lJ1N2ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_lJ1N2pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N25nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N3JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N3ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N3pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N35nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N4JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N4pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N45nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N5JnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N5ZnXEe-0mq43z-9ehA" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N5pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N55nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_lJmj4ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1N6JnXEe-0mq43z-9ehA" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1N6ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N6pnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N65nXEe-0mq43z-9ehA" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N7JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N7ZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N7pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N75nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_lJmj8ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_lJmj4pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N8pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N85nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N9JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N9ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N9pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N95nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N-JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N-ZnXEe-0mq43z-9ehA" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N-pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N-5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N_JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N_ZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N_pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1N_5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OAJnXEe-0mq43z-9ehA" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OAZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OApnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OA5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OBJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OBZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OBpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OB5nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OCJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OCZnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OCpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_lJmj7ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OC5nXEe-0mq43z-9ehA" elementId="title" name="Title"/>
    <parameters xmi:id="_lJ1ODJnXEe-0mq43z-9ehA" elementId="message" name="Message"/>
    <parameters xmi:id="_lJ1ODZnXEe-0mq43z-9ehA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_lJ1ODpnXEe-0mq43z-9ehA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_lJ1OD5nXEe-0mq43z-9ehA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_lJ1OEJnXEe-0mq43z-9ehA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_lJ1OEZnXEe-0mq43z-9ehA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_lJ1OEpnXEe-0mq43z-9ehA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_lJ1OE5nXEe-0mq43z-9ehA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_lJ1OFJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OFZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OFpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OF5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OGJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OGZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OGpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OG5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OHJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OHZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OHpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_lJmj8JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OH5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OIJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OIZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OIpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OI5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OJJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_lJ1OJZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1OJpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OJ5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OKJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OKZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OKpnXEe-0mq43z-9ehA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1OK5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OLJnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_lJmj0ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OLZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OLpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OL5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OMJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OMZnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_lJmj2ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OMpnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OM5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ONJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ONZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ONpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ON5nXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_lJmj0JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OOJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_lJmj6ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OOZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OOpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OO5nXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OPJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OPZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OPpnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_lJmj6pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OP5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OQJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OQZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OQpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OQ5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ORJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_lJmj05nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ORZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ORpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OR5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OSJnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_lJmj45nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OSZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OSpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OS5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OTJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OTZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OTpnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OT5nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OUJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OUZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OUpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OU5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OVJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_lJmj3JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OVZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OVpnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_lJmj1pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OV5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OWJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OWZnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_lJmj4JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OWpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OW5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OXJnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OXZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OXpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OX5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OYJnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OYZnXEe-0mq43z-9ehA" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1OYpnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OY5nXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OZJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_lJmj5pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OZZnXEe-0mq43z-9ehA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_lJ1OZpnXEe-0mq43z-9ehA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_lJ1OZ5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_lJmj6JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OaJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OaZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OapnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Oa5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ObJnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ObZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1ObpnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_lJmj4pnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1Ob5nXEe-0mq43z-9ehA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_lJ1OcJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OcZnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OcpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_lJmj15nXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1Oc5nXEe-0mq43z-9ehA" elementId="kind" name="Kind"/>
    <parameters xmi:id="_lJ1OdJnXEe-0mq43z-9ehA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_lJ1OdZnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OdpnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_lJmj15nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Od5nXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_lJmj4ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OeJnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OeZnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OepnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Oe5nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_lJmj25nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OfJnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OfZnXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_lJmj75nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OfpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_lJmj65nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Of5nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OgJnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_lJmj9ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OgZnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_lJ1OgpnXEe-0mq43z-9ehA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_lJ1Og5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_lJmj3ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OhJnXEe-0mq43z-9ehA" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_lJmj6JnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ1OhZnXEe-0mq43z-9ehA" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_lJ1OhpnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_lJmj55nXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1Oh5nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_lJmj8pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OiJnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_lJmj5JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OiZnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_lJmj7JnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ1OipnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ6F0JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ6F0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ6F0pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_lJmj2pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ6F05nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_lJmj5pnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lJ6F1JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_lJmj3ZnXEe-0mq43z-9ehA">
    <parameters xmi:id="_lJ6F1ZnXEe-0mq43z-9ehA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_lqax4JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqdOIJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqd1MJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqecQJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqecQZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqfDUJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqfqYJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqfqYZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqgRcJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqgRcZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqg4gJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqg4gZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqhfkJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqhfkZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqmYEJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqmYEZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqm_IJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqnmMJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqoNQJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqo0UJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqpbYJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqqCcJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqqCcZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqqpgJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqrQkJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqr3oJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqsesJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqsesZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqtFwJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqtFwZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqts0JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lquT4JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lquT4ZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqu68JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqu68ZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqviAJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqviAZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqviApnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqviA5nXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqwJEJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqwJEZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqwJEpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqwwIJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqwwIZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqxXMJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqxXMZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqx-QJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqylUJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqzMYJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqzMYZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lqzzcJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq0agJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq1BkJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq1ooJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq1ooZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq2PsJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq2PsZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq2PspnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq22wJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq22wZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq3d0JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq3d0ZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4E4JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4E4ZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4E4pnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4r8JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4r8ZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4r8pnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4r85nXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq4r9JnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq5TAJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq5TAZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq5TApnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq56EJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq56EZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq56EpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq6hIJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq6hIZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq6hIpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq6hI5nXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq7IMJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq7IMZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq7vQJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq7vQZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq7vQpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq8WUJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq89YJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq89YZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq9kcJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq9kcZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq9kcpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq-LgJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq-LgZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq-LgpnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq-ykJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq-ykZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq_ZoJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq_ZoZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lq_ZopnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lrAAsJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lrAAsZnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <commands xmi:id="_lrAnwJnXEe-0mq43z-9ehA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_lJmj9ZnXEe-0mq43z-9ehA"/>
  <addons xmi:id="_lIg-sJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_lIg-sZnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_lIg-spnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_lIg-s5nXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_lIg-tJnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_lIg-tZnXEe-0mq43z-9ehA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_lIg-tpnXEe-0mq43z-9ehA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_lIg-t5nXEe-0mq43z-9ehA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_lIg-uJnXEe-0mq43z-9ehA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_lIg-uZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_lI4LEJnXEe-0mq43z-9ehA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_lJmj0JnXEe-0mq43z-9ehA" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_lJmj0ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_lJmj0pnXEe-0mq43z-9ehA" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_lJmj05nXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_lJmj1JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_lJmj1ZnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_lJmj1pnXEe-0mq43z-9ehA" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_lJmj15nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_lJmj2JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_lJmj2ZnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_lJmj2pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_lJmj25nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_lJmj3JnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_lJmj3ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_lJmj3pnXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_lJmj35nXEe-0mq43z-9ehA" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_lJmj4JnXEe-0mq43z-9ehA" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_lJmj4ZnXEe-0mq43z-9ehA" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_lJmj4pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_lJmj45nXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_lJmj5JnXEe-0mq43z-9ehA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_lJmj5ZnXEe-0mq43z-9ehA" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_lJmj5pnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_lJmj55nXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_lJmj6JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_lJmj6ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_lJmj6pnXEe-0mq43z-9ehA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_lJmj65nXEe-0mq43z-9ehA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_lJmj7JnXEe-0mq43z-9ehA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_lJmj7ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_lJmj7pnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_lJmj75nXEe-0mq43z-9ehA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_lJmj8JnXEe-0mq43z-9ehA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_lJmj8ZnXEe-0mq43z-9ehA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_lJmj8pnXEe-0mq43z-9ehA" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_lJmj85nXEe-0mq43z-9ehA" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_lJmj9JnXEe-0mq43z-9ehA" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_lJmj9ZnXEe-0mq43z-9ehA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
