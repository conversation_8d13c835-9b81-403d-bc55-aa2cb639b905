package ar.gov.mpd.concursobackend.experience.application.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * DTO for work experience request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExperienceRequestDto {

    @NotBlank(message = "Company is required")
    private String company;

    @NotBlank(message = "Position is required")
    private String position;

    @NotNull(message = "Start date is required")
    private LocalDate startDate;

    private LocalDate endDate;

    private String description;

    private String comments;

    private String location;

    private String technologies;

    private String achievements;
}