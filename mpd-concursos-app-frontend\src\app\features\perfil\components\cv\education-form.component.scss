// ===== IMPORTAR ESTILOS BASE =====
@import '../../../../../styles/variables';
@import '../../../../../styles/mixins';

// ===== CONTENEDOR PRINCIPAL =====
.education-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;

  // ===== HEADER =====
  .form-header {
    text-align: center;
    margin-bottom: 32px;

    .form-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--font-size-xl);
      font-weight: 600;
      color: var(--color-text-primary);

      .fas {
        font-size: var(--font-size-2xl);
        color: var(--color-primary);
      }
    }

    .form-subtitle {
      margin: 0;
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
    }
  }

  // ===== FORMULARIO PRINCIPAL =====
  .dynamic-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
  }

  // ===== GRID DE CAMPOS =====
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    align-items: start;

    .form-field {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);

      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }

  // ===== CAMPOS ESPECÍFICOS =====
  .field-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    display: block;

    &.required::after {
      content: ' *';
      color: #ef4444;
    }
  }

  .field-help {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 4px;
    line-height: 1.4;
  }

  // ===== TEXTAREA GLASSMORPHISM =====
  .field-textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.3s ease;

    &::placeholder {
      color: transparent !important;
    }

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
      background: rgba(255, 255, 255, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.error {
      border-color: var(--error-color);

      &:focus {
        border-color: var(--error-color);
        box-shadow: 0 0 0 3px rgba(var(--error-color-rgb), 0.1);
      }
    }
  }

  .field-errors {
    margin-top: 6px;

    .error-message {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 0.75rem;
      color: #dc2626;
      margin: 0 0 4px 0;
      line-height: 1.4;

      .material-icons {
        font-size: 14px;
      }
    }
  }

  // ===== CHECKBOX =====
  .checkbox-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      cursor: pointer;
      font-size: var(--font-size-sm);
      line-height: 1.5;
      color: var(--color-text-primary);

      .checkbox-input {
        margin: 0;
        width: 1.25rem;
        height: 1.25rem;
        accent-color: var(--color-primary);
        cursor: pointer;
      }

      .checkbox-text {
        flex: 1;
        user-select: none;
      }
    }
  }

  // ===== ACCIONES =====
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color, #e5e7eb);
    margin-top: 1rem;
    gap: 1rem;

    .action-group {
      display: flex;
      gap: 0.75rem;
      align-items: center;
    }

    @media (max-width: 640px) {
      flex-direction: column;
      gap: 1rem;

      .action-group {
        width: 100%;
        justify-content: center;
      }
    }
  }

  // ===== HELP TEXT =====
  .form-help {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;

    .help-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 1rem;
      font-weight: 600;
      color: #1e40af;
      margin: 0 0 1rem 0;

      .material-icons {
        font-size: 1.25rem;
        color: #3b82f6;
      }
    }

    .help-text {
      color: #6b7280;
      font-size: 1rem;
      margin: 0;
      line-height: 1.5;
    }
  }

  // ===== SECCIONES DEL FORMULARIO =====
  .form-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin: 0 0 var(--spacing-lg) 0;
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
      padding-bottom: var(--spacing-xs);
      border-bottom: 2px solid var(--color-primary);

      .fas {
        font-size: var(--font-size-lg);
        color: var(--color-primary);
      }
    }

    &.document-section {
      border: 2px solid rgba(245, 158, 11, 0.3);
      border-radius: 12px;
      padding: 2rem;
      background: rgba(245, 158, 11, 0.05);
      margin-bottom: 2rem;

      .section-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }
    }
  }

  // ===== ALERTAS =====
  .validation-alerts {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .alert {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid;

      .material-icons {
        flex-shrink: 0;
        margin-top: 2px;
      }

      .alert-content {
        flex: 1;

        strong {
          display: block;
          margin-bottom: 8px;
        }

        ul {
          margin: 0;
          padding-left: 16px;

          li {
            margin-bottom: 4px;
            line-height: 1.4;
          }
        }
      }

      &.alert-error {
        background-color: #fef2f2;
        border-color: #ef4444;
        color: #dc2626;

        .material-icons {
          color: #ef4444;
        }
      }

      &.alert-warning {
        background-color: #fffbeb;
        border-color: #f59e0b;
        color: #d97706;

        .material-icons {
          color: #f59e0b;
        }
      }
    }
  }

  // ===== INDICADOR DE ESTADO MEJORADO =====
  .form-status {
    margin: 1.5rem 0;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.25rem;
      border-radius: 12px;
      font-size: 0.875rem;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid;
      position: relative;
      overflow: hidden;

      // Efecto de brillo sutil
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &.enhanced:hover::before {
        left: 100%;
      }

      .status-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        flex-shrink: 0;
        transition: all 0.3s ease;

        .material-icons {
          font-size: 1.5rem;
          transition: transform 0.3s ease;
        }
      }

      .status-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .status-text {
          font-weight: 600;
          font-size: 1rem;
          line-height: 1.2;
        }

        .status-description {
          font-size: 0.8rem;
          opacity: 0.8;
          line-height: 1.3;
        }
      }

      // Estado válido
      &.valid {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
        border-color: rgba(16, 185, 129, 0.3);
        color: #059669;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);

        .status-icon {
          background: rgba(16, 185, 129, 0.15);
          color: #059669;

          .material-icons {
            color: #10b981;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
        }
      }

      // Estado inválido
      &.invalid {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
        border-color: rgba(245, 158, 11, 0.3);
        color: #d97706;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);

        .status-icon {
          background: rgba(245, 158, 11, 0.15);
          color: #d97706;

          .material-icons {
            color: #f59e0b;
            animation: pulse 2s infinite;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(245, 158, 11, 0.2);
        }
      }

      // Animación para el icono de advertencia
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.1);
        }
      }

      // Responsive
      @media (max-width: 640px) {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem;

        .status-icon {
          width: 2rem;
          height: 2rem;

          .material-icons {
            font-size: 1.25rem;
          }
        }

        .status-content {
          align-items: center;
        }
      }
    }
  }

  // ===== INFORMACIÓN DE DOCUMENTOS MEJORADA =====
  .document-upload-info {
    margin-bottom: 1.5rem;

    .info-box {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem 1.25rem;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.04));
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      color: #ffffff;

      .fas {
        font-size: 1.5rem;
        color: #60a5fa;
        flex-shrink: 0;
        margin-top: 0.125rem;
      }

      .info-content {
        flex: 1;

        p {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          line-height: 1.4;
          color: #ffffff;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            font-weight: 600;
            color: #ffffff;
          }
        }
      }
    }
  }
}
