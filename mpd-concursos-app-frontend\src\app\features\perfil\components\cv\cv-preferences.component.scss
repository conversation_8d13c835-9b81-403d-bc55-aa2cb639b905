/**
 * Estilos del Componente de Preferencias del CV
 * 
 * @description Estilos con glassmorphism y diseño moderno para configuración
 * <AUTHOR> Agent
 * @date 2025-06-21
 * @version 1.0.0
 */

.cv-preferences {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  
  // ===== HEADER =====
  .preferences-header {
    text-align: center;
    margin-bottom: 2rem;
    
    h2 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: var(--text-primary);
      font-size: 1.75rem;
      font-weight: 600;
      
      i {
        color: var(--primary-color);
        font-size: 2rem;
      }
    }
    
    p {
      color: var(--text-secondary);
      margin: 0;
      font-size: 1rem;
    }
  }
  
  // ===== CONTENIDO PRINCIPAL =====
  .preferences-content {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    overflow: hidden;
    margin-bottom: 2rem;
  }
  
  // ===== PESTAÑAS =====
  .preferences-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    overflow-x: auto;
    
    .tab-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 1.5rem;
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      font-size: 0.9rem;
      font-weight: 500;
      
      i {
        font-size: 1.1rem;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
      }
      
      &.active {
        background: var(--primary-color);
        color: white;
        border-bottom: 3px solid var(--primary-color);
      }
    }
  }
  
  // ===== CONTENIDO DE PESTAÑAS =====
  .tab-content {
    padding: 2rem;
    
    .tab-panel {
      h3 {
        margin: 0 0 1.5rem 0;
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
      }
    }
  }
  
  // ===== FORMULARIOS =====
  .preferences-form {
    .form-section {
      margin-bottom: 2rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
    }
    
    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .form-field {
      label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        font-size: 0.9rem;
        transition: all 0.3s ease;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
      
      &.checkbox-field {
        label {
          display: flex;
          align-items: flex-start;
          gap: 0.5rem;
          cursor: pointer;
          
          input[type="checkbox"] {
            width: auto;
            margin: 0;
            margin-top: 0.125rem;
            accent-color: var(--primary-color);
          }
        }
        
        small {
          display: block;
          margin-top: 0.25rem;
          color: var(--text-secondary);
          font-size: 0.8rem;
          line-height: 1.3;
        }
      }
    }
  }
  
  // ===== FILTROS GUARDADOS =====
  .saved-filters-section {
    .filters-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      
      p {
        margin: 0;
        color: var(--text-secondary);
      }
      
      .btn-primary {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    
    .filters-list {
      .filter-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: var(--primary-color);
        }
        
        .filter-info {
          flex: 1;
          
          h4 {
            margin: 0 0 0.25rem 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
          }
          
          p {
            margin: 0 0 0.5rem 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
          }
          
          .filter-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            
            span {
              color: var(--text-secondary);
              font-size: 0.8rem;
            }
          }
        }
        
        .filter-actions {
          display: flex;
          gap: 0.5rem;
          
          button {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-size: 0.8rem;
            
            i {
              font-size: 1rem;
            }
          }
        }
      }
      
      .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--text-secondary);
        
        i {
          font-size: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }
        
        p {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 500;
        }
        
        small {
          font-size: 0.9rem;
        }
      }
    }
  }
  
  // ===== ACCIONES GLOBALES =====
  .preferences-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    
    button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all 0.3s ease;
      
      i {
        font-size: 1.1rem;
      }
    }
  }
  
  // ===== BOTONES =====
  .btn-primary {
    background: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
    
    &:hover {
      background: var(--primary-color-dark);
      border-color: var(--primary-color-dark);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
    }
  }
  
  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }
  }
  
  .btn-danger {
    background: rgba(var(--error-color-rgb), 0.1);
    color: var(--error-color);
    border: 1px solid rgba(var(--error-color-rgb), 0.2);
    
    &:hover {
      background: rgba(var(--error-color-rgb), 0.15);
      border-color: var(--error-color);
      transform: translateY(-2px);
    }
  }
}

// ===== RESPONSIVE =====
@media (max-width: 768px) {
  .cv-preferences {
    .preferences-tabs {
      .tab-button {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
        
        span {
          display: none;
        }
      }
    }
    
    .tab-content {
      padding: 1rem;
    }
    
    .preferences-form {
      .form-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }
    }
    
    .saved-filters-section {
      .filters-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }
      
      .filter-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        
        .filter-actions {
          justify-content: center;
        }
      }
    }
    
    .preferences-actions {
      flex-direction: column;
      
      button {
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .cv-preferences {
    .preferences-header {
      h2 {
        font-size: 1.5rem;
        
        i {
          font-size: 1.75rem;
        }
      }
    }
    
    .preferences-tabs {
      .tab-button {
        flex: 1;
        justify-content: center;
        padding: 0.75rem 0.5rem;
        
        span {
          display: none;
        }
      }
    }
  }
}
