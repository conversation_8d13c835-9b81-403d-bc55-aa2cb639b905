/**
 * Estilos del Componente Contenedor del Sistema CV
 *
 * @description Estilos con glassmorphism y diseño moderno
 * <AUTHOR> Agent
 * @date 2025-06-22
 * @version 3.0.0
 */

@import '../../../../../styles/variables';
@import '../../../../../styles/unified-glassmorphism-mixins';

.cv-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  min-height: 100vh;

  // ===== HEADER DEL CV =====
  .cv-header {
    @include glassmorphism-card('primary', 'large', true);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 16px 16px 0 0;
    }

    .cv-header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-lg);
      flex-wrap: wrap;
      margin-bottom: 1.5rem;

      .cv-title-section {
        flex: 1;
        min-width: 200px;

        .cv-title {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          margin: 0 0 var(--spacing-xs) 0;
          font-size: clamp(1.5rem, 4vw, 2rem);
          font-weight: 700;
          color: #f9fafb; // Color sólido y visible
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); // Sombra más fuerte para mejor legibilidad

          // Remover gradient que causa el efecto difuminado
          // background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
          // -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
          // background-clip: text;

          .fas {
            font-size: clamp(1.5rem, 4vw, 2rem);
            color: var(--primary-color);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
          }
        }

        .cv-subtitle {
          margin: 0;
          font-size: 1.1rem;
          color: var(--text-secondary);
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      .cv-actions {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
        flex-wrap: wrap;

        app-custom-button {
          @include glassmorphism-hover;
        }
      }
    }

    .cv-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        .stat-number {
          display: block;
          font-size: clamp(1.5rem, 3vw, 2rem);
          font-weight: 700;
          color: var(--primary-color);
          line-height: 1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
          display: block;
          font-size: 0.875rem;
          color: var(--text-secondary);
          margin-top: 0.5rem;
          font-weight: 500;
        }

        .stat-date {
          display: block;
          font-size: 0.75rem;
          color: var(--text-tertiary);
          margin-top: 0.25rem;
          font-weight: 400;
        }
      }
    }
  }

  // ===== SECCIÓN DE BÚSQUEDA =====
  .cv-search-section {
    margin-bottom: var(--spacing-2xl);

    .search-container {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
      margin-bottom: var(--spacing-md);

      .search-input-wrapper {
        flex: 1;
        position: relative;

        .search-icon {
          position: absolute;
          left: var(--spacing-md);
          top: 50%;
          transform: translateY(-50%);
          color: var(--color-text-secondary);
          font-size: var(--font-size-lg);
        }

        .search-input {
          width: 100%;
          padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) var(--spacing-3xl);
          border: var(--glass-border);
          border-radius: var(--radius-lg);
          background: var(--glass-bg-secondary);
          backdrop-filter: var(--glass-backdrop-filter);
          color: var(--color-text-primary);
          font-size: var(--font-size-base);
          transition: var(--transition-base);

          &::placeholder {
            color: var(--color-text-secondary);
          }

          &:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px var(--color-primary-alpha-10);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        .clear-search-btn {
          position: absolute;
          right: var(--spacing-sm);
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: var(--color-text-secondary);
          cursor: pointer;
          padding: var(--spacing-xs);
          border-radius: 50%;
          transition: var(--transition-fast);

          &:hover {
            background: var(--glass-bg-secondary);
            color: var(--color-text-primary);
          }
        }
      }

      .filters-toggle-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-lg);
        background: var(--glass-bg-secondary);
        border: var(--glass-border);
        border-radius: var(--radius-lg);
        color: var(--color-text-primary);
        cursor: pointer;
        transition: var(--transition-base);
        font-size: var(--font-size-sm);
        font-weight: 500;

        &:hover {
          background: var(--glass-bg-primary-hover);
          border-color: var(--color-primary);
        }

        &.active {
          background: var(--color-primary);
          color: var(--color-white);
          border-color: var(--color-primary);
        }
      }
    }

    .filters-panel {
      background: var(--glass-bg-primary);
      backdrop-filter: var(--glass-backdrop-filter);
      border: var(--glass-border);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      animation: slideDown var(--transition-base);

      .filters-content {
        .filters-placeholder {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          color: var(--color-text-secondary);
          font-style: italic;
          margin: 0;
        }
      }
    }
  }

  // ===== CONTENIDO PRINCIPAL =====
  .cv-content {

    // Estado vacío con glassmorphism
    .empty-state-content {
      text-align: center;
      padding: var(--spacing-3xl) var(--spacing-2xl);

      .fas {
        font-size: 4rem;
        color: #9ca3af;
        margin-bottom: var(--spacing-lg);
        text-shadow: 0 0 20px rgba(156, 163, 175, 0.3);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        display: block;
      }

      h3 {
        font-size: var(--font-size-xl);
        font-weight: 700;
        margin: 0 0 var(--spacing-md) 0;
        color: #f9fafb;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

        // Gradient text effect
        background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        color: #d1d5db;
        font-size: var(--font-size-base);
        line-height: 1.6;
        margin: 0 0 var(--spacing-2xl) 0;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-weight: 400;
      }

      .empty-state-actions {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
        margin-top: var(--spacing-xl);

        .btn {
          margin: 0; // Reset margin from global btn styles
          min-width: 160px; // Ensure consistent button width
        }
      }
    }

    // Contenedor de tabs mejorado
    .cv-tabs-container {
      margin-bottom: var(--spacing-2xl);

      .cv-tabs-header {
        display: flex;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 0.5rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        .cv-tab-button {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: var(--spacing-xs);
          padding: 1rem 1.5rem;
          background: transparent;
          border: none;
          border-radius: 8px;
          color: var(--text-secondary);
          font-size: var(--font-size-sm);
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;

          .fas {
            font-size: var(--font-size-lg);
            transition: all 0.3s ease;
          }

          .tab-count {
            background: rgba(var(--primary-color-rgb), 0.2);
            color: var(--primary-color);
            font-size: var(--font-size-xs);
            font-weight: 600;
            padding: var(--spacing-xs) var(--spacing-xs);
            border-radius: var(--radius-lg);
            min-width: var(--spacing-lg);
            text-align: center;
          }

          &:hover:not(.active) {
            background: rgba(255, 255, 255, 0.08);
            color: var(--text-primary);
            transform: translateY(-2px);

            .fas {
              color: var(--primary-color);
            }
          }

          &.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);

            .fas {
              color: white;
            }

            .tab-count {
              background: rgba(255, 255, 255, 0.2);
              color: white;
            }
          }

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.3);
          }
        }
      }
    }

    // Contenido de tabs con drag & drop
    .cv-tab-content {
      .tab-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--color-border-light);

        h3 {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-size: var(--font-size-xl);
          font-weight: 600;
          color: var(--color-text-primary);
          margin: 0;

          .fas {
            font-size: var(--font-size-xl);
            color: var(--color-primary);
          }
        }

        .tab-actions {
          display: flex;
          align-items: center;
          gap: var(--spacing-md);

          .item-count {
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
            font-weight: 500;
          }
        }
      }

      // Lista con drag & drop
      .drag-drop-list {
        min-height: 200px;

        .drag-drop-item {
          display: flex;
          align-items: flex-start;
          gap: var(--spacing-md);
          background: var(--glass-bg-secondary);
          border: var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--spacing-lg);
          margin-bottom: var(--spacing-md);
          transition: var(--transition-base);
          cursor: move;

          &:hover {
            background: var(--glass-bg-primary-hover);
            border-color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
          }

          &.cdk-drag-dragging {
            opacity: 0.8;
            transform: rotate(2deg);
            box-shadow: var(--shadow-xl);
          }

          .drag-handle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: var(--spacing-2xl);
            height: var(--spacing-2xl);
            color: var(--color-text-secondary);
            cursor: grab;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);

            &:hover {
              background: var(--glass-bg-secondary);
              color: var(--color-primary);
            }

            &:active {
              cursor: grabbing;
            }

            .fas {
              font-size: var(--font-size-lg);
            }
          }

          .item-content {
            flex: 1;

            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: var(--spacing-md);

              .title-section {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: var(--spacing-xs);

                h4 {
                  font-size: var(--font-size-lg);
                  font-weight: 600;
                  color: var(--color-text-primary);
                  margin: 0;
                }

                .validation-status {
                  .status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: var(--spacing-xs);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-xl);
                    font-size: var(--font-size-xs);
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    .fas {
                      font-size: var(--font-size-sm);
                    }

                    &.status-pending {
                      background: var(--color-warning-alpha-10);
                      color: var(--color-warning);
                      border: 1px solid var(--color-warning-alpha-20);
                    }

                    &.status-validated {
                      background: var(--color-success-alpha-10);
                      color: var(--color-success);
                      border: 1px solid var(--color-success-alpha-20);
                    }

                    &.status-rejected {
                      background: var(--color-error-alpha-10);
                      color: var(--color-error);
                      border: 1px solid var(--color-error-alpha-20);
                    }
                  }
                }
              }

              .item-actions {
                display: flex;
                gap: var(--spacing-xs);

                // Botón específico para ver documentos
                .btn-info {
                  background-color: var(--color-info, #17a2b8);
                  color: white;
                  border: 1px solid var(--color-info, #17a2b8);

                  &:hover {
                    background-color: var(--color-info-dark, #138496);
                    border-color: var(--color-info-dark, #138496);
                  }

                  .fas {
                    color: white;
                  }
                }
              }
            }

            .item-details {
              .company,
              .institution {
                font-weight: 500;
                color: var(--color-text-secondary);
                margin: 0 0 var(--spacing-xs) 0;
              }

              .dates {
                font-size: var(--font-size-sm);
                color: var(--color-text-tertiary);
                margin: 0 0 var(--spacing-sm) 0;
              }

              // Información de empresa y ubicación
              .company-info {
                display: flex;
                flex-wrap: wrap;
                gap: var(--spacing-md);
                margin-bottom: var(--spacing-sm);

                .company,
                .location {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  margin: 0;
                  color: var(--color-text-secondary);
                  font-weight: 500;

                  .fas {
                    font-size: var(--font-size-base);
                    color: var(--color-primary);
                  }
                }
              }

              // Sección de fechas mejorada
              .dates-section {
                margin: 0 0 var(--spacing-md) 0;

                .dates-info {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  font-size: var(--font-size-sm);
                  color: var(--color-text-tertiary);
                  margin-bottom: var(--spacing-xs);

                  .fas {
                    font-size: var(--font-size-base);
                    color: var(--color-primary);
                  }

                  .date-range {
                    font-weight: 500;
                  }

                  .date-secondary {
                    color: var(--color-text-secondary);
                    font-style: italic;
                    font-size: var(--font-size-xs);
                  }

                  .duration {
                    color: var(--color-text-secondary);
                    font-style: italic;
                    font-size: var(--font-size-xs);
                  }
                }

                .current-job-badge,
                .ongoing-badge {
                  display: inline-flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  background: var(--color-success-alpha-10);
                  color: var(--color-success);
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border-radius: var(--radius-xl);
                  font-size: var(--font-size-xs);
                  font-weight: 600;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;

                  .fas {
                    font-size: var(--font-size-sm);
                  }
                }
              }

              // Sección de descripción
              .description-section {
                margin-bottom: var(--spacing-md);

                .description {
                  color: var(--color-text-secondary);
                  line-height: 1.6;
                  margin: 0 0 var(--spacing-sm) 0;
                  white-space: pre-wrap;
                }

                .expand-btn {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  font-size: var(--font-size-sm);
                  color: var(--color-primary);
                  background: none;
                  border: none;
                  padding: var(--spacing-xs) 0;
                  cursor: pointer;
                  transition: var(--transition-fast);

                  &:hover {
                    color: var(--color-primary-hover);
                  }

                  .fas {
                    font-size: var(--font-size-base);
                  }
                }
              }

              // Información adicional (logros y tecnologías)
              .additional-info {
                border-top: 1px solid var(--color-border-light);
                padding-top: var(--spacing-md);
                margin-top: var(--spacing-md);

                h5 {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  font-size: var(--font-size-sm);
                  font-weight: 600;
                  color: var(--color-text-primary);
                  margin: 0 0 var(--spacing-sm) 0;

                  .fas {
                    font-size: var(--font-size-base);
                    color: var(--color-primary);
                  }
                }

                .achievements {
                  margin-bottom: var(--spacing-md);

                  .achievements-list {
                    margin: 0;
                    padding-left: var(--spacing-lg);
                    color: var(--color-text-secondary);

                    li {
                      margin-bottom: var(--spacing-xs);
                      line-height: 1.5;
                    }
                  }
                }

                .technologies {
                  .tech-badges {
                    display: flex;
                    flex-wrap: wrap;
                    gap: var(--spacing-xs);

                    .tech-badge {
                      padding: var(--spacing-xs) var(--spacing-sm);
                      background: var(--color-primary-alpha-10);
                      color: var(--color-primary);
                      border-radius: var(--radius-xl);
                      font-size: var(--font-size-xs);
                      font-weight: 600;
                      text-transform: uppercase;
                      letter-spacing: 0.5px;
                    }
                  }
                }
              }

              .badges {
                display: flex;
                gap: var(--spacing-xs);
                flex-wrap: wrap;

                .type-badge,
                .status-badge {
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border-radius: var(--radius-xl);
                  font-size: var(--font-size-xs);
                  font-weight: 600;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }

                .type-badge {
                  background: var(--color-primary-alpha-10);
                  color: var(--color-primary);
                }

                .status-badge {
                  &.status-completed {
                    background: var(--color-success-alpha-10);
                    color: var(--color-success);
                  }

                  &.status-in_progress,
                  &.status-in-progress {
                    background: var(--color-warning-alpha-10);
                    color: var(--color-warning);
                  }

                  &.status-suspended,
                  &.status-abandoned {
                    background: var(--color-error-alpha-10);
                    color: var(--color-error);
                  }
                }
              }
            }
          }
        }

        // Preview personalizado para drag
        .drag-preview {
          background: var(--glass-bg-primary);
          backdrop-filter: var(--glass-backdrop-filter);
          border: 1px solid var(--color-primary);
          border-radius: var(--radius-lg);
          padding: var(--spacing-md);
          box-shadow: var(--shadow-xl);

          .preview-content {
            h4 {
              font-size: var(--font-size-base);
              font-weight: 600;
              color: var(--color-text-primary);
              margin: 0 0 var(--spacing-xs) 0;
            }

            p {
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
              margin: 0;
            }
          }
        }

        // Zona vacía
        .empty-drop-zone {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: var(--spacing-3xl) var(--spacing-2xl);
          border: 2px dashed var(--color-border-light);
          border-radius: var(--radius-lg);
          text-align: center;

          .fas {
            font-size: var(--font-size-3xl);
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-md);
          }

          p {
            color: var(--color-text-secondary);
            margin: 0 0 var(--spacing-lg) 0;
          }
        }
      }

      // Placeholder de drop
      .cdk-drop-list-dragging .drag-drop-item:not(.cdk-drag-dragging) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }

      .cdk-drag-placeholder {
        opacity: 0;
        background: var(--color-primary-alpha-10);
        border: 2px dashed var(--color-primary);
        border-radius: var(--radius-lg);
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;

        &::before {
          content: 'Soltar aquí';
          color: var(--color-primary);
          font-weight: 500;
        }
      }

      .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }

        // Estados de carga y error
        .loading-state,
        .error-state,
        .empty-experiences,
        .empty-education {
          text-align: center;
          padding: var(--spacing-3xl) var(--spacing-2xl);
          background: var(--glass-bg-secondary);
          border-radius: var(--radius-lg);
          border: var(--glass-border);
          .fas {
            font-size: var(--font-size-3xl);
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-md);
            display: block;
          }

          .error-icon {
            color: var(--color-error) !important;
          }

          p {
            color: var(--color-text-secondary);
            margin: 0 0 var(--spacing-lg) 0;
          }
        }

        // Cards de experiencias y educación
        .experience-cards,
        .education-cards {
          display: grid;
          gap: var(--spacing-lg);

          .section-header {
            margin-bottom: 1rem;

            h4 {
              color: var(--text-primary);
              font-size: 1.1rem;
              font-weight: 600;
              margin: 0;
            }
          }

          .experience-card,
          .education-card {
            background: var(--glass-bg-secondary);
            border: var(--glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-xl);

              &::before {
                opacity: 1;
              }
            }

            // Información específica de educación
            .education-specific-info {
              margin: var(--spacing-md) 0;
              padding: var(--spacing-sm) 0;
              border-top: 1px solid rgba(255, 255, 255, 0.1);

              .specific-details {
                display: flex;
                flex-wrap: wrap;
                gap: var(--spacing-sm);

                .detail-item {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  padding: var(--spacing-xs) var(--spacing-sm);
                  background: rgba(59, 130, 246, 0.1);
                  border: 1px solid rgba(59, 130, 246, 0.2);
                  border-radius: var(--radius-sm);
                  font-size: var(--font-size-sm);

                  .fas {
                    font-size: 16px;
                    color: var(--color-primary);
                  }

                  .detail-label {
                    font-weight: 500;
                    color: var(--color-text-secondary);
                  }

                  .detail-value {
                    color: var(--color-text-primary);
                    font-weight: 600;
                  }
                }
              }

              .experience-technologies {
                margin-bottom: 0.75rem;

                .tech-chips {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 0.5rem;

                  .tech-chip {
                    background: rgba(var(--accent-color-rgb), 0.2);
                    color: var(--accent-color);
                    padding: 0.25rem 0.5rem;
                    border-radius: 8px;
                    font-size: 0.8rem;
                    font-weight: 500;
                  }
                }
              }

              .experience-achievements {
                margin-bottom: 0.75rem;

                h6 {
                  color: var(--text-primary);
                  font-size: 0.9rem;
                  font-weight: 600;
                  margin: 0 0 0.5rem 0;
                }

                .achievements-list {
                  margin: 0;
                  padding-left: 1.2rem;

                  li {
                    color: var(--text-secondary);
                    font-size: 0.9rem;
                    line-height: 1.4;
                    margin-bottom: 0.25rem;

                    &:last-child {
                      margin-bottom: 0;
                    }
                  }
                }
              }

              .experience-document,
              .education-document {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: var(--text-secondary);
                font-size: 0.9rem;

                .fas {
                  font-size: 1rem;
                  color: var(--primary-color);
                }
              }
            }

            // Sección de descripción expandible
            .description-section {
              margin: var(--spacing-md) 0;

              .description {
                color: var(--color-text-secondary);
                line-height: 1.6;
                margin-bottom: var(--spacing-sm);
              }

              .expand-btn {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                color: var(--color-primary);
                font-size: var(--font-size-sm);
                padding: var(--spacing-xs) 0;

                &:hover {
                  color: var(--color-primary-light);
                }

                .fas {
                  font-size: 18px;
                }
              }
            }

            .experience-content,
            .education-content {
              .experience-header,
              .education-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: var(--spacing-md);

                .experience-position,
                .education-title {
                  font-size: var(--font-size-lg);
                  font-weight: 600;
                  color: var(--color-text-primary);
                  margin: 0;
                  flex: 1;
                }

                .experience-actions,
                .education-actions {
                  display: flex;
                  gap: var(--spacing-xs);
                }
              }

              .experience-company,
              .education-institution {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                color: var(--color-text-secondary);
                margin-bottom: var(--spacing-sm);
                font-weight: 500;

                .fas {
                  font-size: var(--font-size-base);
                  color: var(--color-primary);
                }

                .experience-location {
                  color: var(--color-text-tertiary);
                }
              }

              .education-type {
                display: flex;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-sm);

                .education-type-badge,
                .education-status-badge {
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border-radius: var(--radius-2xl);
                  font-size: var(--font-size-xs);
                  font-weight: 600;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }

                .education-type-badge {
                  background: var(--color-primary-alpha-10);
                  color: var(--color-primary);
                }

                .education-status-badge {
                  &.status-completed {
                    background: var(--color-success-alpha-10);
                    color: var(--color-success);
                  }

                  &.status-in_progress {
                    background: var(--color-warning-alpha-10);
                    color: var(--color-warning);
                  }

                  &.status-suspended,
                  &.status-abandoned {
                    background: var(--color-error-alpha-10);
                    color: var(--color-error);
                  }
                }
              }

              .experience-dates,
              .education-dates {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                color: var(--color-text-secondary);
                margin-bottom: var(--spacing-md);
                font-size: var(--font-size-sm);

                .fas {
                  font-size: var(--font-size-base);
                }

                .current-job-badge,
                .ongoing-badge {
                  background: var(--color-success);
                  color: var(--color-white);
                  padding: var(--spacing-xs) var(--spacing-xs);
                  border-radius: var(--radius-lg);
                  font-size: var(--font-size-xs);
                  font-weight: 600;
                  margin-left: var(--spacing-xs);
                }
              }

              .experience-description {
                margin-bottom: var(--spacing-md);

                p {
                  color: var(--color-text-secondary);
                  line-height: 1.6;
                  margin: 0;
                }
              }

              .experience-technologies {
                margin-bottom: var(--spacing-md);

                .tech-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: var(--spacing-xs);

                  .tech-tag {
                    background: var(--color-primary-alpha-10);
                    color: var(--color-primary);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-xl);
                    font-size: var(--font-size-xs);
                    font-weight: 500;
                  }
                }
              }

              .experience-document,
              .education-document {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                color: var(--color-success);
                font-size: var(--font-size-sm);
                font-weight: 500;

                .fas {
                  font-size: var(--font-size-base);
                }
              }
            }
          }
        }
      }
    }
  }

// ===== ESTILOS ADICIONALES PARA GLASSMORPHISM =====

/* Mejoras para contenedores de acciones */
.cv-actions,
.empty-state-actions,
.tab-actions {
  .btn {
    margin: 0 var(--spacing-xs); // Espaciado horizontal entre botones

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

/* Mejoras para textos glassmorphism */
.card-title,
h1, h2, h3, h4, h5, h6 {
  &:not(.no-glassmorphism) {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

    // Gradient text effect solo para card-title, NO para cv-title
    &.card-title {
      background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

/* CV Title específico - sin gradient para mejor legibilidad */
.cv-title {
  color: #f9fafb !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

/* Mejoras para párrafos y texto secundario */
p, .cv-subtitle, .card-subtitle {
  &:not(.no-glassmorphism) {
    color: #d1d5db;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

/* Mejoras para iconos Material */
.fas {
  &:not(.no-glassmorphism) {
    text-shadow: 0 0 10px rgba(156, 163, 175, 0.4);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
}

// ===== ANIMACIONES =====
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// ===== RESPONSIVE =====
@media (max-width: 768px) {
  .cv-container {
    padding: var(--spacing-md);

    .cv-header {
      padding: var(--spacing-lg);

      .cv-header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;

        .cv-title-section {
          text-align: center;

          .cv-title {
            font-size: clamp(1.25rem, 5vw, 1.5rem);
            flex-direction: column;
            gap: 0.5rem;
          }

          .cv-progress-indicator {
            margin-top: 1.5rem;

            .progress-text {
              text-align: center;
              display: block;
              margin-top: 0.5rem;
            }
          }
        }

        .cv-actions {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          justify-content: center;

          .btn {
            width: 100%;
            justify-content: center;
          }
        }
      }

      .cv-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
      }
    }

    .cv-search-section {
      .search-container {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .cv-content {
      .tab-panel {
        .tab-panel-header {
          flex-direction: column;
          gap: var(--spacing-md);
          align-items: stretch;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .cv-container {
    padding: 0.75rem;

    .cv-header-content {
      .cv-title-section {
        .cv-title {
          font-size: 1.25rem;

          .fas {
            font-size: 1.25rem;
          }
        }

        .cv-subtitle {
          font-size: 1rem;
        }

        .cv-progress-indicator {
          .progress-bar {
            height: 6px;
          }

          .progress-text {
            font-size: 0.75rem;
          }
        }
      }

      .cv-actions {
        .btn {
          padding: 0.75rem 1rem;
          font-size: 0.875rem;

          .fas {
            font-size: 1rem;
          }
        }
      }
    }

    .cv-stats {
      grid-template-columns: 1fr;
      gap: 0.75rem;

      .stat-item {
        padding: 0.75rem;

        .stat-number {
          font-size: 1.25rem;
        }

        .stat-label {
          font-size: 0.75rem;
        }
      }
    }

    .cv-content {
      .tab-panel {
        .tab-panel-header {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
          text-align: center;

          .panel-title {
            font-size: 1.25rem;
            justify-content: center;
          }
        }
      }

      .empty-experiences,
      .empty-education,
      .empty-state-content {
        padding: 2rem 1rem;

        .fas {
          font-size: 2.5rem;
        }

        h3, h4 {
          font-size: 1.125rem;
        }

        p {
          font-size: 0.875rem;
        }

        .empty-state-actions {
          flex-direction: column;
          gap: 0.75rem;

          .btn {
            width: 100%;
          }
        }
      }
    }
  }
}
