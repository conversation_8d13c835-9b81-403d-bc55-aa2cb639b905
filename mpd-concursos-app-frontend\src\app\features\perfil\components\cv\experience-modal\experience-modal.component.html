<!-- Experience Modal -->
<app-modal-base
  [isOpen]="isOpen"
  [config]="modalConfig()"
  (close)="onClose()">
  
  <!-- Modal Title -->
  <div slot="title">
    <i class="fas fa-briefcase modal-icon"></i>
    {{ modalTitle() }}
  </div>
  
  <!-- Modal Subtitle -->
  <div slot="subtitle" *ngIf="modalSubtitle()">
    {{ modalSubtitle() }}
  </div>
  
  <!-- Modal Body - Experience Form -->
  <div class="experience-modal-content">
    <app-experience-form
      #experienceForm
      [experience]="experience"
      [mode]="mode"
      [isLoading]="isLoading"
      [isInModal]="true"
      (save)="onSave($event)"
      (cancel)="onClose()"
      (validationChange)="onValidationChange($event)">
    </app-experience-form>
  </div>
  
  <!-- Modal Footer -->
  <div slot="footer" class="experience-modal-footer">
    
    <!-- Delete <PERSON><PERSON> (solo en modo edición) -->
    <div class="footer-left">
      <app-custom-button
        *ngIf="showDeleteButton()"
        label="Eliminar"
        variant="warn"
        size="medium"
        icon="trash"
        [disabled]="isLoading"
        (buttonClick)="onDelete()">
      </app-custom-button>
    </div>
    
    <!-- Action Buttons -->
    <div class="footer-right">
      <!-- Cancel Button -->
      <app-custom-button
        label="Cancelar"
        variant="stroked"
        size="medium"
        [disabled]="isLoading"
        (buttonClick)="onClose()">
      </app-custom-button>
      
      <!-- Save Button -->
      <button
        *ngIf="mode !== 'view'"
        type="submit"
        form="experience-form"
        class="custom-button primary"
        [disabled]="!canSave()"
        [class.loading]="isLoading">
        <i *ngIf="!isLoading" class="fas fa-save"></i>
        <i *ngIf="isLoading" class="fas fa-sync-alt spinning"></i>
        {{ mode === 'create' ? 'Agregar' : 'Guardar' }}
      </button>

      <!-- Close Button (solo en modo vista) -->
      <app-custom-button
        *ngIf="mode === 'view'"
        label="Cerrar"
        variant="primary"
        size="medium"
        icon="close"
        (buttonClick)="onClose()">
      </app-custom-button>
    </div>
  </div>
</app-modal-base>
