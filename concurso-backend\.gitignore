HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Application Properties ###
src/main/resources/application-local.properties
src/main/resources/application-dev.properties
src/main/resources/application-prod.properties

### Environment Files ###
.env
*.env
.env.*

### Logs ###
*.log
logs/

### Document Storage ###
document-storage/

# H2 Database files
src/main/resources/static/concursosH2bd/concursosbd.mv.db
concursosH2bd/concursosbd.mv.db
concurso-backend/src/main/resources/static/concursosH2bd/concursosbd.trace.db

# =============================================================================
# ARCHIVOS SENSIBLES ADICIONALES DE SEGURIDAD
# =============================================================================
# Claves privadas y certificados adicionales
*.keystore
*.truststore

# Archivos de configuración con credenciales
**/application-secrets.properties
**/database.properties
**/credentials.properties

# Archivos de backup que pueden contener datos sensibles
*.sql
*.dump
*.backup