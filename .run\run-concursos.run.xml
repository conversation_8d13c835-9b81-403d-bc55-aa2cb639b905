<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="run-concursos" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="dev" />
    <additionalParameters>
      <param>
        <option name="enabled" value="true" />
        <option name="name" value="management.endpoint.env.show-values" />
        <option name="value" value="when_authorized" />
      </param>
    </additionalParameters>
    <option name="ALTERNATIVE_JRE_PATH" value="21" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="envFilePaths">
      <option value="$PROJECT_DIR$/concurso-backend/.env" />
    </option>
    <module name="mpd-concursos-app-backend" />
    <selectedOptions>
      <option name="environmentVariables" />
    </selectedOptions>
    <option name="SPRING_BOOT_MAIN_CLASS" value="ar.gov.mpd.concursobackend.ConcursoBackendApplication" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>