/**
 * Estilos para EstadoPerfilWidgetComponent
 * ✅ LIMPIEZA: Estilos extraídos del template inline
 */

.widget-container {
  background: rgba(55, 65, 81, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(156, 163, 175, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.3);
  }
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.widget-icon {
  color: var(--color-primary, #3b82f6);
  font-size: 1.5rem;
}

h3 {
  color: #f9fafb;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.completion-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;

  &.high {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  &.medium {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  &.low {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
}

.widget-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.progress-container {
  margin-bottom: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(75, 85, 99, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;

  &.high {
    background: linear-gradient(90deg, #22c55e, #16a34a);
  }

  &.medium {
    background: linear-gradient(90deg, #f59e0b, #d97706);
  }

  &.low {
    background: linear-gradient(90deg, #ef4444, #dc2626);
  }
}

.status-message {
  margin: 0.5rem 0;

  p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.4;

    &.success {
      color: #22c55e;
    }

    &.warning {
      color: #f59e0b;
    }

    &.error {
      color: #ef4444;
    }
  }
}

// ===== NUEVOS ESTILOS PARA WIDGET EXPANDIBLE =====

.progress-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0.75rem 0;
  font-size: 0.75rem;
  color: #d1d5db;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.summary-label {
  opacity: 0.8;
}

.summary-value {
  font-weight: 600;
  color: #f9fafb;
}

.summary-divider {
  color: rgba(156, 163, 175, 0.5);
  margin: 0 0.5rem;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem;
  background: rgba(75, 85, 99, 0.3);
  border: 1px solid rgba(156, 163, 175, 0.2);
  border-radius: 6px;
  color: #d1d5db;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0.75rem 0;

  &:hover {
    background: rgba(75, 85, 99, 0.5);
    color: #f9fafb;
    transform: translateY(-1px);
  }

  i {
    font-size: 0.7rem;
    transition: transform 0.2s ease;
  }
}

.details-section {
  background: rgba(31, 41, 55, 0.5);
  border: 1px solid rgba(156, 163, 175, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin: 0.75rem 0;
  backdrop-filter: blur(10px);
}

.documents-section {
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #f9fafb;

  i {
    color: var(--color-primary, #3b82f6);
    font-size: 0.8rem;
  }
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.8rem;
  transition: all 0.2s ease;

  &.document-completed {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    color: #22c55e;
  }

  &.document-pending {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: #f59e0b;
  }

  &.document-rejected {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #ef4444;
  }

  &.document-missing {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
    color: #9ca3af;
  }

  i {
    font-size: 0.75rem;
    min-width: 12px;
  }
}

.document-name {
  flex: 1;
  font-weight: 500;
}

.document-status {
  font-size: 0.7rem;
  opacity: 0.8;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;

  &.primary {
    &.complete {
      background: rgba(34, 197, 94, 0.2);
      color: #22c55e;
      border: 1px solid rgba(34, 197, 94, 0.3);

      &:hover {
        background: rgba(34, 197, 94, 0.3);
        transform: translateY(-1px);
      }
    }

    &.incomplete {
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
      border: 1px solid rgba(59, 130, 246, 0.3);

      &:hover {
        background: rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
      }
    }
  }

  &.secondary {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);

    &:hover {
      background: rgba(107, 114, 128, 0.3);
      color: #d1d5db;
      transform: translateY(-1px);
    }
  }
}

// ===== RESPONSIVE DESIGN =====

@media (max-width: 768px) {
  .widget-container {
    padding: 1rem;
  }

  h3 {
    font-size: 1rem;
  }

  .completion-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
  }

  .progress-summary {
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;

    .summary-divider {
      display: none;
    }
  }

  .action-buttons {
    flex-direction: column;
  }

  .details-section {
    padding: 0.75rem;
  }

  .section-title {
    font-size: 0.8rem;
  }

  .document-item {
    font-size: 0.75rem;
    padding: 0.4rem;
  }

  .expand-button {
    font-size: 0.7rem;
    padding: 0.4rem;
  }
}
