/**
 * Estilos del Formulario Inteligente de Experiencias Laborales
 *
 * @description Estilos con glassmorphism y validación visual en tiempo real
 * <AUTHOR> Agent
 * @date 2025-06-20
 * @version 2.0.0
 */

// Importar correcciones de dropdown
@import './cv-dropdown-fixes.scss';

.experience-form {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;


  
  /* ===== ALERTAS DE VALIDACIÓN ===== */
  .validation-alerts {
    margin-bottom: 2rem;

    .alert {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 1rem;

      .material-icons {
        font-size: 1.125rem;
        margin-top: 0.125rem;
      }

      .alert-content {
        flex: 1;

        strong {
          display: block;
          margin-bottom: 0.5rem;
        }

        ul {
          margin: 0;
          padding-left: 1.5rem;

          li {
            margin-bottom: 0.5rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      &.alert-error {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        color: #dc2626;
      }

      &.alert-warning {
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.2);
        color: #d97706;
      }
    }
  }

  /* ===== HEADER DEL FORMULARIO ===== */
  .form-header {
    text-align: center;
    margin-bottom: 2rem;

    .form-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 0.5rem 0;

      .material-icons {
        font-size: 1.5rem;
        color: #3b82f6;
      }
    }

    .form-subtitle {
      color: #6b7280;
      margin: 0 0 1.5rem 0;
      font-size: 1rem;
    }

    /* ===== ALERTA DE DOCUMENTACIÓN OBLIGATORIA ===== */
    .document-requirement-alert {
      margin-top: 1.5rem;

      .alert {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid #f59e0b;
        background: rgba(245, 158, 11, 0.1);
        color: #92400e;
        text-align: left;

        &.alert-warning {
          border-left-color: #f59e0b;
          background: rgba(245, 158, 11, 0.1);
          color: #92400e;
        }

        .fas {
          font-size: 1.5rem;
          color: #f59e0b;
          margin-top: 2px;
        }

        .alert-content {
          flex: 1;

          strong {
            display: block;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #92400e;
          }

          p {
            margin: 0;
            line-height: 1.5;
            font-size: 0.875rem;
          }
        }
      }
    }
  }

  /* ===== SECCIÓN DE DOCUMENTOS ===== */
  .document-section {
    border: 2px solid rgba(245, 158, 11, 0.3);
    border-radius: 12px;
    padding: 2rem;
    background: rgba(245, 158, 11, 0.05);
    margin-bottom: 2rem;

    .section-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #3b82f6;

      .material-icons {
        font-size: 1.125rem;
        color: #3b82f6;
      }

      &.required {
        color: #92400e;
        font-weight: 700;

        .required-indicator {
          color: #dc2626;
          font-size: 1.125rem;
          margin-left: 0.5rem;
        }
      }
    }

    .section-description {
      color: #6b7280;
      font-size: 0.875rem;
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .document-upload-area {
      .file-input-container {
        position: relative;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          border-color: #3b82f6;
          background: rgba(59, 130, 246, 0.05);
        }

        .file-input {
          position: absolute;
          opacity: 0;
          width: 100%;
          height: 100%;
          cursor: pointer;
        }

        .file-input-label {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          cursor: pointer;

          .material-icons {
            font-size: 3rem;
            color: #3b82f6;
          }

          .upload-text {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            strong {
              font-size: 1.125rem;
              color: #1f2937;
            }

            small {
              font-size: 0.75rem;
              color: #6b7280;
            }
          }
        }
      }

      .selected-file {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        border: 1px solid #10b981;
        border-radius: 12px;
        background: rgba(16, 185, 129, 0.1);

        .file-info {
          display: flex;
          align-items: center;
          gap: 1rem;

          .file-icon {
            font-size: 1.5rem;
            color: #10b981;
          }

          .file-details {
            display: flex;
            flex-direction: column;

            .file-name {
              font-weight: 600;
              color: #1f2937;
            }

            .file-size {
              font-size: 0.75rem;
              color: #6b7280;
            }
          }
        }

        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0.5rem;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;

          &.btn-icon {
            width: 2.5rem;
            height: 2.5rem;
          }

          &.btn-error {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;

            &:hover {
              background: rgba(239, 68, 68, 0.2);
            }
          }

          .material-icons {
            font-size: 1.25rem;
          }
        }
      }

      .validation-error {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 0.75rem;
        background: rgba(239, 68, 68, 0.1);
        border-radius: 8px;
        color: #dc2626;
        font-size: 0.875rem;

        .material-icons {
          font-size: 1rem;
        }
      }
    }
  }

  /* ===== FORMULARIO DINÁMICO ===== */
  .dynamic-form {
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-2xl);

      .form-field {
        &.full-width {
          grid-column: 1 / -1;
        }
      }
    }

    /* Campo de checkbox personalizado */
    .checkbox-field {
      .checkbox-label {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        cursor: pointer;
        font-weight: 500;
        color: var(--color-text-primary);

        .checkbox-input {
          display: none;

          &:checked + .checkbox-custom {
            background: var(--color-primary);
            border-color: var(--color-primary);

            &::after {
              opacity: 1;
              transform: scale(1);
            }
          }

          &:disabled + .checkbox-custom {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        .checkbox-custom {
          width: 1.25rem;
          height: 1.25rem;
          border: 2px solid var(--color-border);
          border-radius: var(--radius-sm);
          background: var(--glass-bg-secondary);
          position: relative;
          transition: var(--transition-fast);

          &::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            color: var(--color-white);
            font-size: var(--font-size-sm);
            font-weight: bold;
            opacity: 0;
            transition: var(--transition-fast);
          }
        }

        .checkbox-text {
          flex: 1;
        }
      }

      .field-help {
        margin-top: var(--spacing-xs);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
        margin-bottom: 0;
      }
    }
    
    // Campo de chips
    .chips-field {
      .field-label {
        display: block;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        
        .required-indicator {
          color: var(--error-color);
          margin-left: 0.25rem;
        }
      }
      
      .chips-input-container {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        
        .chips-input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          color: var(--text-primary);
          font-size: 0.875rem;
          transition: all 0.3s ease;
          
          &::placeholder {
            color: var(--text-tertiary);
            font-style: italic;
          }
          
          &:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
          }
          
          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
        
        .add-chip-btn {
          padding: 0.75rem;
          background: var(--primary-color);
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover:not(:disabled) {
            background: var(--primary-color-dark);
            transform: translateY(-1px);
          }
          
          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
          
          .material-icons {
            font-size: 1.125rem;
          }
        }
      }
      
      .chips-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        
        .chip {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.15) 0%, rgba(var(--primary-color-rgb), 0.08) 100%);
          border: 1px solid rgba(var(--primary-color-rgb), 0.25);
          border-radius: 20px;
          padding: 0.5rem 0.875rem;
          font-size: 0.875rem;
          color: #ffffff; // ✅ ACCESIBILIDAD: Cambio a blanco para mejor contraste sobre fondo oscuro
          font-weight: 500;
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
          animation: chipAppear 0.3s ease;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);
            border-color: rgba(var(--primary-color-rgb), 0.4);
          }

          .chip-text {
            font-weight: 500;
            letter-spacing: 0.025em;
          }

          .chip-remove {
            background: rgba(var(--error-color-rgb), 0.1);
            border: 1px solid rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;

            &:hover:not(:disabled) {
              background: var(--error-color);
              color: white;
              transform: scale(1.1);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }

            .material-icons {
              font-size: 0.875rem;
            }
          }
        }
      }
      
      .chips-counter {
        font-size: 0.75rem;
        color: var(--text-secondary);
        text-align: right;
        margin-bottom: 0.5rem;
      }
      
      .field-help {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 0.5rem 0;
      }
      
      .field-errors {
        .error-message {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--error-color);
          font-size: 0.875rem;
          margin: 0.25rem 0;
          
          .material-icons {
            font-size: 1rem;
          }
        }
      }
    }
  }
  
  // ===== INDICADOR DE ESTADO MEJORADO =====
  .form-status {
    margin: 1.5rem 0;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.25rem;
      border-radius: 12px;
      font-size: 0.875rem;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid;
      position: relative;
      overflow: hidden;

      // Efecto de brillo sutil
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &.enhanced:hover::before {
        left: 100%;
      }

      .status-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        flex-shrink: 0;
        transition: all 0.3s ease;

        .fas {
          font-size: 1.5rem;
          transition: transform 0.3s ease;
        }
      }

      .status-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .status-text {
          font-weight: 600;
          font-size: 1rem;
          line-height: 1.2;
        }

        .status-description {
          font-size: 0.8rem;
          opacity: 0.8;
          line-height: 1.3;
        }
      }

      // Estado válido
      &.valid {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
        border-color: rgba(16, 185, 129, 0.3);
        color: #059669;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);

        .status-icon {
          background: rgba(16, 185, 129, 0.15);
          color: #059669;

          .material-icons {
            color: #10b981;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
        }
      }

      // Estado inválido
      &.invalid {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
        border-color: rgba(245, 158, 11, 0.3);
        color: #d97706;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);

        .status-icon {
          background: rgba(245, 158, 11, 0.15);
          color: #d97706;

          .material-icons {
            color: #f59e0b;
            animation: pulse 2s infinite;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(245, 158, 11, 0.2);
        }
      }

      // Animación para el icono de advertencia
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.1);
        }
      }

      // Responsive
      @media (max-width: 640px) {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem;

        .status-icon {
          width: 2rem;
          height: 2rem;

          .material-icons {
            font-size: 1.25rem;
          }
        }

        .status-content {
          align-items: center;
        }
      }
    }
  }
  
  // ===== ACCIONES DEL FORMULARIO =====
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 2rem;

    .actions-left,
    .actions-right {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    // ===== ESTILO ESPECIAL PARA BOTÓN GUARDAR VÁLIDO =====
    ::ng-deep .save-button-valid {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(16, 185, 129, 0.7)) !important;
      border: 1px solid rgba(16, 185, 129, 0.4) !important;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25) !important;
      color: #ffffff !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: linear-gradient(135deg, rgba(16, 185, 129, 1), rgba(16, 185, 129, 0.8)) !important;
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.35) !important;
        transform: translateY(-2px) !important;
      }

      &:active {
        transform: translateY(0) !important;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3) !important;
      }

      // Efecto de pulso sutil para indicar que está listo
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.1));
        border-radius: inherit;
        z-index: -1;
        animation: validButtonGlow 2s ease-in-out infinite alternate;
      }
    }
  }
  
  // ===== INFORMACIÓN DE AYUDA =====
  .form-help {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    
    .help-section {
      margin-bottom: 1.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .help-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.75rem 0;
        
        .material-icons {
          font-size: 1.125rem;
          color: var(--primary-color);
        }
      }
      
      .help-list {
        margin: 0;
        padding-left: 1.25rem;
        color: var(--text-secondary);
        
        li {
          margin-bottom: 0.5rem;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .help-text {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  // ===== SECCIÓN DE DOCUMENTACIÓN =====
  .documentation-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    .section-header {
      margin-bottom: 1.5rem;

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.5rem 0;

        .material-icons {
          color: var(--primary-color);
          font-size: 1.25rem;
        }

        .required-indicator {
          color: var(--error-color);
          margin-left: 0.25rem;
        }
      }

      .section-subtitle {
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.5;
        font-size: 0.875rem;
      }
    }
  }

  // ===== ESTILOS PARA TEXTAREA GLASSMORPHISM =====
  .field-textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.3s ease;

    &::placeholder {
      color: transparent !important;
    }

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
      background: rgba(255, 255, 255, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  // Estilos para labels de textarea
  .form-field {
    .field-label {
      display: block;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 0.75rem;
      font-size: 0.875rem;

      &.required::after {
        content: ' *';
        color: var(--error-color);
      }
    }

    .field-help {
      font-size: 0.75rem;
      color: var(--text-secondary);
      margin-top: 0.5rem;
      line-height: 1.4;
    }

    .field-errors {
      margin-top: 0.5rem;

      .error-message {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--error-color);
        font-size: 0.75rem;
        margin: 0.25rem 0;

        .material-icons {
          font-size: 1rem;
        }
      }
    }
  }
}

// ===== ANIMACIONES =====
@keyframes chipAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes validButtonGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

// ===== RESPONSIVE =====
@media (max-width: 768px) {
  .experience-form {
    .dynamic-form {
      .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        
        .form-field {
          &.full-width {
            grid-column: 1;
          }
        }
      }
    }
    
    .form-actions {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
      
      .actions-left,
      .actions-right {
        justify-content: center;
      }
    }
    
    .chips-field {
      .chips-input-container {
        flex-direction: column;
        
        .add-chip-btn {
          align-self: flex-start;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .experience-form {
    .form-header {
      .form-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
      }
    }
    
    .chips-field {
      .chips-list {
        .chip {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }

  // ===== INFORMACIÓN DE DOCUMENTOS MEJORADA =====
  .document-upload-info {
    margin-bottom: 1.5rem;

    .info-box {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem 1.25rem;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.04));
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      color: #ffffff;

      .fas {
        font-size: 1.5rem;
        color: #60a5fa;
        flex-shrink: 0;
        margin-top: 0.125rem;
      }

      .info-content {
        flex: 1;

        p {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          line-height: 1.4;
          color: #ffffff;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            font-weight: 600;
            color: #ffffff;
          }
        }
      }
    }
  }
}
