// Variables locales para el componente
$border-radius-md: 6px;
$transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// ===== BADGE BASE STYLES =====
.inscription-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: $border-radius-md;
  transition: $transition-smooth;
  white-space: nowrap;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  i {
    font-size: 0.875em;
    opacity: 0.9;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// ===== SIZE VARIANTS =====
.badge-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.6875rem;
  line-height: 1.2;

  i {
    font-size: 0.75em;
  }
}

.badge-medium {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1.3;
}

.badge-large {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.4;

  i {
    font-size: 1em;
  }
}

// ===== STATE-SPECIFIC STYLES =====

// ACTIVE - En Proceso (Azul)
.badge-active {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(37, 99, 235, 0.25) 100%);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.25) 0%,
      rgba(37, 99, 235, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }
}

// PENDING - Pendiente (Naranja)
.badge-pending {
  background: linear-gradient(135deg,
    rgba(249, 115, 22, 0.15) 0%,
    rgba(234, 88, 12, 0.25) 100%);
  color: #f97316;
  border-color: rgba(249, 115, 22, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(249, 115, 22, 0.25) 0%,
      rgba(234, 88, 12, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
  }
}

// COMPLETED_WITH_DOCS - Completa con Docs (Verde)
.badge-completed-docs {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.15) 0%,
    rgba(22, 163, 74, 0.25) 100%);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.25) 0%,
      rgba(22, 163, 74, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
  }
}

// COMPLETED_PENDING_DOCS - Docs Pendientes (Amarillo)
.badge-completed-pending {
  background: linear-gradient(135deg,
    rgba(234, 179, 8, 0.15) 0%,
    rgba(202, 138, 4, 0.25) 100%);
  color: #eab308;
  border-color: rgba(234, 179, 8, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(234, 179, 8, 0.25) 0%,
      rgba(202, 138, 4, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(234, 179, 8, 0.2);
  }
}

// FROZEN - Congelada (Azul Gris)
.badge-frozen {
  background: linear-gradient(135deg,
    rgba(100, 116, 139, 0.15) 0%,
    rgba(71, 85, 105, 0.25) 100%);
  color: #64748b;
  border-color: rgba(100, 116, 139, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(100, 116, 139, 0.25) 0%,
      rgba(71, 85, 105, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.2);
  }
}

// APPROVED - Aprobada (Verde Oscuro)
.badge-approved {
  background: linear-gradient(135deg,
    rgba(21, 128, 61, 0.15) 0%,
    rgba(22, 101, 52, 0.25) 100%);
  color: #15803d;
  border-color: rgba(21, 128, 61, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(21, 128, 61, 0.25) 0%,
      rgba(22, 101, 52, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(21, 128, 61, 0.2);
  }
}

// REJECTED - Rechazada (Rojo)
.badge-rejected {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.15) 0%,
    rgba(220, 38, 38, 0.25) 100%);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(239, 68, 68, 0.25) 0%,
      rgba(220, 38, 38, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }
}

// CANCELLED - Cancelada (Gris)
.badge-cancelled {
  background: linear-gradient(135deg,
    rgba(107, 114, 128, 0.15) 0%,
    rgba(75, 85, 99, 0.25) 100%);
  color: #6b7280;
  border-color: rgba(107, 114, 128, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(107, 114, 128, 0.25) 0%,
      rgba(75, 85, 99, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.2);
  }
}

// UNKNOWN - Estado Desconocido (Púrpura)
.badge-unknown {
  background: linear-gradient(135deg,
    rgba(147, 51, 234, 0.15) 0%,
    rgba(126, 34, 206, 0.25) 100%);
  color: #9333ea;
  border-color: rgba(147, 51, 234, 0.3);

  &:hover {
    background: linear-gradient(135deg,
      rgba(147, 51, 234, 0.25) 0%,
      rgba(126, 34, 206, 0.35) 100%);
    box-shadow: 0 4px 12px rgba(147, 51, 234, 0.2);
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .inscription-status-badge {
    font-size: 0.6875rem;
    padding: 0.25rem 0.5rem;
    gap: 0.25rem;

    i {
      font-size: 0.75em;
    }
  }
}

// ===== ACCESSIBILITY =====
.inscription-status-badge {
  &:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
  }

  // Mejorar contraste para usuarios con discapacidades visuales
  @media (prefers-contrast: high) {
    border-width: 2px;
    font-weight: 700;
  }

  // Reducir animaciones para usuarios sensibles al movimiento
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    &:hover {
      transform: none;
    }
  }
}
