<!-- Componente Contenedor del Sistema CV - Versión Simplificada -->
<div class="cv-container">

  <!-- Header del CV -->
  <div class="cv-header">
    <div class="cv-header-content">
      <div class="cv-title-section">
        <h2 class="cv-title">
          <i class="fas fa-file-alt"></i>
          Curriculum Vitae
        </h2>
        <p class="cv-subtitle" *ngIf="userProfile">
          {{ userProfile.firstName }} {{ userProfile.lastName }}
        </p>
      </div>

      <div class="cv-actions">
        <button class="btn btn-primary" (click)="exportCv()" [disabled]="!hasData()">
          <i class="fas fa-download"></i>
          Exportar CV
        </button>

        <button class="btn btn-secondary" (click)="refreshData()">
          <i class="fas fa-sync-alt"></i>
          Actualizar
        </button>
      </div>
    </div>
  </div>

  <!-- Componente de búsqueda avanzada -->
  <div *ngIf="hasData()">
    <app-cv-search
      [experiences]="cvState().experiences.data"
      [education]="cvState().education.data"
      [isLoading]="isAnyLoading()"
      (filtersChange)="onFiltersChange($event)">
    </app-cv-search>
  </div>

  <!-- Contenido principal con funcionalidades avanzadas -->
  <div class="cv-content">

    <!-- Estado vacío -->
    <div *ngIf="!hasData() && !isAnyLoading()" class="cv-empty-state">
      <div class="empty-state-content">
          <i class="fas fa-file-alt empty-icon"></i>
          <h3>Tu CV está vacío</h3>
          <p>Comienza agregando tu experiencia laboral y educación para crear un CV completo.</p>

          <div class="empty-state-actions">
            <button class="btn btn-primary" (click)="addExperience()">
              <i class="fas fa-briefcase"></i>
              Agregar Experiencia
            </button>

            <button class="btn btn-secondary" (click)="addEducation()">
              <i class="fas fa-graduation-cap"></i>
              Agregar Educación
            </button>
          </div>
        </div>
      </div>

    <!-- Tabs del CV -->
    <div *ngIf="hasData() || isAnyLoading()" class="cv-tabs-container">
      <div class="cv-tabs-header">
        <button
          *ngFor="let tab of tabs(); let i = index"
          class="cv-tab-button"
          [class.active]="tab.isActive"
          (click)="onTabChange(tab.id)">
          <i class="fas fa-{{ tab.icon }}"></i>
          <span>{{ tab.label }}</span>
          <span class="tab-count" *ngIf="tab.count > 0">{{ tab.count }}</span>
        </button>
      </div>

      <!-- Contenido con drag & drop mejorado -->
      <div class="cv-tab-content">
        <!-- Tab de Experiencia Laboral con Drag & Drop -->
        <div *ngIf="activeTab() === 'experience'">
          <app-custom-card>
            <div class="tab-header">
              <h3>Experiencia Laboral</h3>
              <div class="tab-actions">
                <span class="item-count">{{ cvState().experiences.data.length }} experiencias</span>
                <app-custom-button
                  [label]="'Agregar Experiencia'"
                  [variant]="'primary'"
                  [icon]="'add'"
                  (buttonClick)="addExperience()">
                </app-custom-button>
              </div>
            </div>

            <!-- Lista con drag & drop -->
            <div
              class="drag-drop-list"
              cdkDropList
              [cdkDropListData]="cvState().experiences.data"
              (cdkDropListDropped)="onExperienceDrop($event)">

              <div
                class="drag-drop-item experience-item"
                *ngFor="let experience of cvState().experiences.data; trackBy: trackByExperienceId"
                cdkDrag>

                <!-- Handle de arrastre -->
                <div class="drag-handle" cdkDragHandle>
                  <i class="fas fa-grip-vertical"></i>
                </div>

                <!-- Contenido de la experiencia -->
                <div class="item-content">
                  <div class="item-header">
                    <div class="title-section">
                      <h4>{{ experience.position || 'Sin título' }}</h4>
                      <!-- Indicador de estado de validación de documentos -->
                      <div class="validation-status">
                        <span class="status-badge status-pending"
                              title="Documentación pendiente de validación administrativa">
                          <i class="fas fa-clock"></i>
                          Pendiente de validación
                        </span>
                      </div>
                    </div>
                    <div class="item-actions">
                      <!-- Botón para ver documento probatorio -->
                      <button
                        *ngIf="experience.documentUrl"
                        class="btn btn-icon btn-info"
                        (click)="viewExperienceDocument(experience)"
                        title="Ver documento probatorio">
                        <i class="fas fa-file-pdf"></i>
                      </button>
                      <button class="btn btn-icon btn-secondary" (click)="editExperience(experience)">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-icon btn-error" (click)="deleteExperience(experience)">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="item-details">
                    <div class="company-info">
                      <p class="company">
                        <i class="fas fa-building"></i>
                        {{ experience.company || 'Sin empresa' }}
                      </p>
                      <p class="location" *ngIf="experience.location">
                        <i class="fas fa-map-marker-alt"></i>
                        {{ experience.location }}
                      </p>
                    </div>

                    <div class="dates-section">
                      <div class="dates-info">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="date-range">{{ formatExperienceDates(experience) }}</span>
                        <span class="duration" *ngIf="getExperienceDuration(experience)">
                          • {{ getExperienceDuration(experience) }}
                        </span>
                      </div>
                      <div class="current-job-badge" *ngIf="experience.isCurrentJob">
                        <i class="fas fa-briefcase"></i>
                        <span>Trabajo Actual</span>
                      </div>
                    </div>

                    <div class="description-section" *ngIf="experience.description">
                      <p class="description">{{ experience.description }}</p>
                      <button
                        class="btn btn-link expand-btn"
                        *ngIf="experience.description && experience.description.length > 150 && experience.id"
                        (click)="toggleExperienceExpanded(experience.id!)">
                        <i class="fas fa-{{ isExperienceExpanded(experience.id!) ? 'chevron-up' : 'chevron-down' }}"></i>
                        {{ isExperienceExpanded(experience.id!) ? 'Ver menos' : 'Ver más' }}
                      </button>
                    </div>

                    <!-- Logros y tecnologías -->
                    <div class="additional-info" *ngIf="experience.achievements?.length || experience.technologies?.length">
                      <div class="achievements" *ngIf="experience.achievements?.length">
                        <h5>
                          <i class="fas fa-star"></i>
                          Logros principales
                        </h5>
                        <ul class="achievements-list">
                          <li *ngFor="let achievement of experience.achievements">{{ achievement }}</li>
                        </ul>
                      </div>

                      <div class="technologies" *ngIf="experience.technologies?.length">
                        <h5>
                          <i class="fas fa-code"></i>
                          Tecnologías
                        </h5>
                        <div class="tech-badges">
                          <span class="tech-badge" *ngFor="let tech of experience.technologies">{{ tech }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Preview personalizado para drag -->
                <div class="drag-preview" *cdkDragPreview>
                  <div class="preview-content">
                    <h4>{{ experience.position || 'Sin título' }}</h4>
                    <p>{{ experience.company || 'Sin empresa' }}</p>
                  </div>
                </div>
              </div>

              <!-- Placeholder cuando está vacío -->
              <div class="empty-drop-zone" *ngIf="cvState().experiences.data.length === 0">
                <i class="fas fa-briefcase"></i>
                <p>No hay experiencias laborales</p>
                <app-custom-button
                  [label]="'Agregar Primera Experiencia'"
                  [variant]="'primary'"
                  [icon]="'add'"
                  (buttonClick)="addExperience()">
                </app-custom-button>
              </div>
            </div>
          </app-custom-card>
        </div>

        <!-- Tab de Educación con Drag & Drop -->
        <div *ngIf="activeTab() === 'education'">
          <app-custom-card>
            <div class="tab-header">
              <h3>Educación</h3>
              <div class="tab-actions">
                <span class="item-count">{{ cvState().education.data.length }} estudios</span>
                <app-custom-button
                  [label]="'Agregar Educación'"
                  [variant]="'primary'"
                  [icon]="'add'"
                  (buttonClick)="addEducation()">
                </app-custom-button>
              </div>
            </div>

            <!-- Lista con drag & drop -->
            <div
              class="drag-drop-list"
              cdkDropList
              [cdkDropListData]="cvState().education.data"
              (cdkDropListDropped)="onEducationDrop($event)">

              <div
                class="drag-drop-item education-item"
                *ngFor="let education of cvState().education.data; trackBy: trackByEducationId"
                cdkDrag>

                <!-- Handle de arrastre -->
                <div class="drag-handle" cdkDragHandle>
                  <i class="fas fa-grip-vertical"></i>
                </div>

                <!-- Contenido de la educación -->
                <div class="item-content">
                  <div class="item-header">
                    <div class="title-section">
                      <h4>{{ education.title || 'Sin título' }}</h4>
                      <!-- Indicador de estado de validación de documentos -->
                      <div class="validation-status">
                        <span class="status-badge status-pending"
                              title="Documentación pendiente de validación administrativa">
                          <i class="fas fa-clock"></i>
                          Pendiente de validación
                        </span>
                      </div>
                    </div>
                    <div class="item-actions">
                      <!-- Botón para ver documento probatorio -->
                      <button
                        *ngIf="education.documentUrl"
                        class="btn btn-icon btn-info"
                        (click)="viewEducationDocument(education)"
                        title="Ver documento probatorio">
                        <i class="fas fa-file-pdf"></i>
                      </button>
                      <button class="btn btn-icon btn-secondary" (click)="editEducation(education)">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-icon btn-error" (click)="deleteEducation(education)">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="item-details">
                    <div class="institution-info">
                      <p class="institution">
                        <i class="fas fa-university"></i>
                        {{ education.institution || 'Sin institución' }}
                      </p>
                    </div>

                    <div class="dates-section">
                      <div class="dates-info">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="date-range">{{ formatEducationDates(education) }}</span>
                        <!-- Información secundaria de fechas (ej: "En curso") -->
                        <span class="date-secondary" *ngIf="getEducationDateInfo(education).secondary">
                          • {{ getEducationDateInfo(education).secondary }}
                        </span>
                        <!-- Duración solo si debe mostrarse según el tipo -->
                        <span class="duration" *ngIf="shouldShowEducationDuration(education) && getEducationDuration(education)">
                          • {{ getEducationDuration(education) }}
                        </span>
                      </div>
                    </div>

                    <!-- Información adicional específica por tipo de educación -->
                    <div class="education-additional-info" *ngIf="getEducationAdditionalInfo(education)">
                      <div class="additional-details">
                        <div class="detail-item" *ngFor="let detail of getEducationAdditionalInfo(education)">
                          <i class="fas fa-{{ detail.icon }}"></i>
                          <span class="detail-label">{{ detail.label }}:</span>
                          <span class="detail-value">{{ detail.value }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- Descripción o comentarios -->
                    <div class="description-section" *ngIf="education.comments">
                      <p class="description">{{ education.comments }}</p>
                      <button
                        class="btn btn-link expand-btn"
                        *ngIf="education.comments && education.comments.length > 150 && education.id"
                        (click)="toggleEducationExpanded(education.id!)">
                        <i class="fas fa-{{ isEducationExpanded(education.id!) ? 'chevron-up' : 'chevron-down' }}"></i>
                        {{ isEducationExpanded(education.id!) ? 'Ver menos' : 'Ver más' }}
                      </button>
                    </div>

                    <div class="badges">
                      <span class="type-badge">{{ getEducationTypeLabel(education.type) }}</span>
                      <span class="status-badge"
                            [class]="'status-' + (education.status === 'COMPLETED' ? 'completed' : 'in-progress')">
                        {{ getEducationStatusLabel(education.status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Preview personalizado para drag -->
                <div class="drag-preview" *cdkDragPreview>
                  <div class="preview-content">
                    <h4>{{ education.title || 'Sin título' }}</h4>
                    <p>{{ education.institution || 'Sin institución' }}</p>
                  </div>
                </div>
              </div>

              <!-- Placeholder cuando está vacío -->
              <div class="empty-drop-zone" *ngIf="cvState().education.data.length === 0">
                <i class="fas fa-graduation-cap"></i>
                <p>No hay educación registrada</p>
                <app-custom-button
                  [label]="'Agregar Primera Educación'"
                  [variant]="'primary'"
                  [icon]="'add'"
                  (buttonClick)="addEducation()">
                </app-custom-button>
              </div>
            </div>
          </app-custom-card>
        </div>
      </div>
    </div>
  </div>

  <!-- Modales con funcionalidad mejorada -->
  <app-experience-modal
    #experienceModal
    [isOpen]="showExperienceModal()"
    [experience]="selectedExperience()"
    [mode]="experienceModalMode()"
    [isLoading]="isExperienceLoading()"
    (close)="onExperienceModalClose()"
    (save)="onExperienceSave($event)"
    (delete)="onExperienceDelete($event)">
  </app-experience-modal>

  <app-education-modal
    #educationModal
    [isOpen]="showEducationModal()"
    [education]="selectedEducation()"
    [mode]="educationModalMode()"
    [isLoading]="isEducationLoading()"
    (close)="onEducationModalClose()"
    (save)="onEducationSave($event)"
    (delete)="onEducationDelete($event)">
  </app-education-modal>
</div>
