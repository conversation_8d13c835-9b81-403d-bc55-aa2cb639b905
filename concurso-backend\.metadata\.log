!SESSION 2024-11-03 08:33:56.438 -----------------------------------------------
eclipse.buildId=4.33.0.20240905-0613
java.version=21.0.4
java.vendor=Red Hat, Inc.
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=es_AR
Framework arguments:  -product org.eclipse.epp.package.java.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.java.product

!ENTRY ch.qos.logback.classic 1 0 2024-11-03 08:34:00.692
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2024-11-03 08:34:25.263
!MESSAGE Logback config file: B:\CODE\PROYECTOS\mpd-concursos\concurso-backend\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.6.1.20240411-1122.xml

!ENTRY org.eclipse.egit.ui 2 0 2024-11-03 08:34:37.223
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>