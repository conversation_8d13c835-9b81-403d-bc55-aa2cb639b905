<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Estado del Perfil - Mejora Expandible</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .demo-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .demo-title {
            text-align: center;
            color: #f9fafb;
            font-size: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
        }

        /* Estilos del widget (copiados del componente) */
        .widget-container {
            background: rgba(55, 65, 81, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(156, 163, 175, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .widget-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.3);
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .widget-icon {
            color: #3b82f6;
            font-size: 1.5rem;
        }

        h3 {
            color: #f9fafb;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .completion-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .widget-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .progress-container {
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(75, 85, 99, 0.5);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            background: linear-gradient(90deg, #f59e0b, #d97706);
            width: 78%;
        }

        .progress-summary {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0.75rem 0;
            font-size: 0.75rem;
            color: #d1d5db;
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .summary-label {
            opacity: 0.8;
        }

        .summary-value {
            font-weight: 600;
            color: #f9fafb;
        }

        .summary-divider {
            color: rgba(156, 163, 175, 0.5);
            margin: 0 0.5rem;
        }

        .expand-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
            padding: 0.5rem;
            background: rgba(75, 85, 99, 0.3);
            border: 1px solid rgba(156, 163, 175, 0.2);
            border-radius: 6px;
            color: #d1d5db;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.75rem 0;
        }

        .expand-button:hover {
            background: rgba(75, 85, 99, 0.5);
            color: #f9fafb;
            transform: translateY(-1px);
        }

        .details-section {
            background: rgba(31, 41, 55, 0.5);
            border: 1px solid rgba(156, 163, 175, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.75rem 0;
            backdrop-filter: blur(10px);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .details-section.expanded {
            max-height: 500px;
        }

        .documents-section {
            margin-bottom: 1rem;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0 0 0.75rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #f9fafb;
        }

        .section-title i {
            color: #3b82f6;
            font-size: 0.8rem;
        }

        .documents-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .document-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .document-completed {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .document-pending {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .document-missing {
            background: rgba(107, 114, 128, 0.1);
            border: 1px solid rgba(107, 114, 128, 0.2);
            color: #9ca3af;
        }

        .document-item i {
            font-size: 0.75rem;
            min-width: 12px;
        }

        .document-name {
            flex: 1;
            font-weight: 500;
        }

        .document-status {
            font-size: 0.7rem;
            opacity: 0.8;
            font-style: italic;
        }

        .status-message {
            margin: 0.5rem 0;
        }

        .status-message p {
            margin: 0;
            font-size: 0.875rem;
            line-height: 1.4;
            color: #f59e0b;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: auto;
        }

        .action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
        }

        .action-button.primary {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .action-button.primary:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .action-button.secondary {
            background: rgba(107, 114, 128, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(107, 114, 128, 0.3);
        }

        .action-button.secondary:hover {
            background: rgba(107, 114, 128, 0.3);
            color: #d1d5db;
            transform: translateY(-1px);
        }

        .demo-info {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(156, 163, 175, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
            color: #d1d5db;
        }

        .demo-info h2 {
            color: #f9fafb;
            margin-top: 0;
            font-size: 1.2rem;
        }

        .demo-info p {
            margin: 0.5rem 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎯 Widget Estado del Perfil - Expandible</h1>
        
        <!-- Widget Estado del Perfil Mejorado -->
        <div class="widget-container">
            <div class="widget-header">
                <div class="header-content">
                    <i class="fas fa-user-circle widget-icon"></i>
                    <h3>Estado del Perfil</h3>
                </div>
                <div class="completion-badge">78%</div>
            </div>

            <div class="widget-body">
                <!-- Barra de progreso principal -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>

                <!-- Resumen de progreso por categorías -->
                <div class="progress-summary">
                    <div class="summary-item">
                        <span class="summary-label">📋 Datos Personales:</span>
                        <span class="summary-value">71%</span>
                    </div>
                    <div class="summary-divider">|</div>
                    <div class="summary-item">
                        <span class="summary-label">📄 Documentos:</span>
                        <span class="summary-value">85%</span>
                    </div>
                </div>

                <!-- Botón para expandir/contraer detalles -->
                <button class="expand-button" onclick="toggleDetails()">
                    <i class="fas fa-chevron-down" id="expand-icon"></i>
                    <span id="expand-text">Ver Detalles</span>
                </button>

                <!-- Sección expandible con detalles -->
                <div class="details-section" id="details-section">
                    <!-- Documentación Requerida -->
                    <div class="documents-section">
                        <h4 class="section-title">
                            <i class="fas fa-file-medical"></i>
                            Documentación Requerida (4/5)
                        </h4>
                        <div class="documents-list">
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">DNI (Frente/Dorso)</span>
                            </div>
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">CUIL</span>
                            </div>
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">Antecedentes Penales</span>
                            </div>
                            <div class="document-item document-missing">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="document-name">Certificado Profesional</span>
                                <span class="document-status">(vence en 15 días)</span>
                            </div>
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">Certificado Ley Micaela</span>
                            </div>
                        </div>
                    </div>

                    <!-- Documentación Opcional -->
                    <div class="documents-section">
                        <h4 class="section-title">
                            <i class="fas fa-file-plus"></i>
                            Documentación Opcional (2/3)
                        </h4>
                        <div class="documents-list">
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">Capacitación Adicional</span>
                            </div>
                            <div class="document-item document-completed">
                                <i class="fas fa-check-circle"></i>
                                <span class="document-name">Certificado Experiencia</span>
                            </div>
                            <div class="document-item document-missing">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="document-name">Certificado Idiomas</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mensaje de estado -->
                <div class="status-message">
                    <p>Falta 1 documento requerido. Tu perfil está casi completo.</p>
                </div>

                <!-- Botones de acción -->
                <div class="action-buttons">
                    <button class="action-button primary">
                        <i class="fas fa-edit"></i>
                        Completar Perfil
                    </button>
                    <button class="action-button secondary">
                        <i class="fas fa-file-upload"></i>
                        Gestionar Documentos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-info">
        <h2>✨ Características del Widget Expandible</h2>
        <p><strong>• Porcentaje global inteligente:</strong> Combina datos personales (40%) + documentos requeridos (60%)</p>
        <p><strong>• Desglose por categorías:</strong> Muestra progreso separado de datos personales y documentación</p>
        <p><strong>• Vista expandible:</strong> Detalles completos bajo demanda sin saturar la vista inicial</p>
        <p><strong>• Estados específicos:</strong> Documentos completados, pendientes, faltantes con iconos claros</p>
        <p><strong>• Vencimientos próximos:</strong> Alerta sobre documentos que expiran pronto</p>
        <p><strong>• Acciones contextuales:</strong> Botones específicos para completar perfil y gestionar documentos</p>
        <p><strong>• Diseño conservado:</strong> Mantiene completamente el estilo glassmorphism existente</p>
    </div>

    <script>
        let isExpanded = false;

        function toggleDetails() {
            const detailsSection = document.getElementById('details-section');
            const expandIcon = document.getElementById('expand-icon');
            const expandText = document.getElementById('expand-text');

            isExpanded = !isExpanded;

            if (isExpanded) {
                detailsSection.classList.add('expanded');
                expandIcon.className = 'fas fa-chevron-up';
                expandText.textContent = 'Ocultar Detalles';
            } else {
                detailsSection.classList.remove('expanded');
                expandIcon.className = 'fas fa-chevron-down';
                expandText.textContent = 'Ver Detalles';
            }
        }
    </script>
</body>
</html>
