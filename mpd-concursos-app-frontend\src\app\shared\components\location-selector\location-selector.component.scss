/* Location Selector - Estilo coherente con el formulario de registro */

.location-selector {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.location-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.required-indicator {
  color: #ff6b6b;
  font-weight: bold;
}

/* Campo País (fijo) */
.country-field {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px 16px;
  backdrop-filter: blur(10px);
}

.country-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: not-allowed;
}

.country-flag {
  font-size: 18px;
  margin-left: 8px;
}

.country-lock {
  font-size: 12px;
  margin-left: 8px;
  opacity: 0.6;
}

/* Campo deshabilitado */
.disabled-field {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  backdrop-filter: blur(5px);
}

.disabled-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: rgba(255, 255, 255, 0.4);
  font-size: 14px;
  cursor: not-allowed;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.3);
    font-style: italic;
  }
}

.disabled-icon {
  font-size: 12px;
  opacity: 0.4;
  margin-left: 8px;
}

/* Información adicional */
.province-info,
.municipality-info {
  margin-top: 6px;
  padding: 8px 12px;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 6px;
  animation: infoSlideIn 0.3s ease-out;
}

.province-details,
.municipality-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.province-code,
.municipality-postal {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.province-region,
.municipality-province {
  color: rgba(255, 255, 255, 0.7);
}

@keyframes infoSlideIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Texto de ayuda */
.field-help {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
}

.help-icon {
  font-size: 12px;
  opacity: 0.7;
}

.help-text {
  line-height: 1.3;
}

/* Resumen de ubicación */
.location-summary {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.08) 0%, rgba(0, 150, 200, 0.05) 100%);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  animation: summarySlideIn 0.4s ease-out;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.summary-icon {
  font-size: 16px;
}

.summary-title {
  color: #00d4ff;
  font-size: 14px;
  font-weight: 600;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.summary-label {
  color: rgba(255, 255, 255, 0.7);
  min-width: 70px;
  font-weight: 500;
}

.summary-value {
  color: #ffffff;
  flex: 1;
}

.summary-code,
.summary-postal {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  margin-left: 4px;
}

@keyframes summarySlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Indicador de validación */
.validation-status {
  margin-top: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.3s ease;
  
  &.valid {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
  }
  
  &.invalid {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
  }
}

.status-icon {
  font-size: 14px;
}

.status-text {
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .location-selector {
    gap: 16px;
  }
  
  .summary-content {
    gap: 10px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .summary-label {
    min-width: auto;
    font-size: 11px;
  }
  
  .summary-value {
    font-size: 12px;
  }
}

/* Estados de accesibilidad */
.country-input:focus-visible,
.disabled-input:focus-visible {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

/* Modo oscuro adicional */
@media (prefers-color-scheme: dark) {
  .country-field,
  .disabled-field {
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.08);
  }
  
  .location-summary {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(0, 150, 200, 0.03) 100%);
    border-color: rgba(0, 212, 255, 0.15);
  }
}

/* Animaciones reducidas para accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .province-info,
  .municipality-info,
  .location-summary {
    animation: none;
  }
  
  .status-indicator {
    transition: none;
  }
}
